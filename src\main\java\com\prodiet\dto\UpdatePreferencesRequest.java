package com.prodiet.dto;

import com.prodiet.entity.DietaryPreference;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public class UpdatePreferencesRequest {

    @NotNull(message = "Excluded meal IDs list is required (can be empty)")
    private List<Long> excludedMealIds;

    @NotNull(message = "Dietary preferences list is required (can be empty)")
    @Valid
    private List<DietaryPreferenceDto> dietaryPreferences;

    // Constructors
    public UpdatePreferencesRequest() {}

    public UpdatePreferencesRequest(List<Long> excludedMealIds, List<DietaryPreferenceDto> dietaryPreferences) {
        this.excludedMealIds = excludedMealIds;
        this.dietaryPreferences = dietaryPreferences;
    }

    // Getters and Setters
    public List<Long> getExcludedMealIds() {
        return excludedMealIds;
    }

    public void setExcludedMealIds(List<Long> excludedMealIds) {
        this.excludedMealIds = excludedMealIds;
    }

    public List<DietaryPreferenceDto> getDietaryPreferences() {
        return dietaryPreferences;
    }

    public void setDietaryPreferences(List<DietaryPreferenceDto> dietaryPreferences) {
        this.dietaryPreferences = dietaryPreferences;
    }

    // Inner DTO class for dietary preferences
    public static class DietaryPreferenceDto {
        
        @NotNull(message = "Preference text is required")
        private String preferenceText;

        private DietaryPreference.PreferenceType preferenceType = DietaryPreference.PreferenceType.OTHER;

        // Constructors
        public DietaryPreferenceDto() {}

        public DietaryPreferenceDto(String preferenceText, DietaryPreference.PreferenceType preferenceType) {
            this.preferenceText = preferenceText;
            this.preferenceType = preferenceType;
        }

        // Getters and Setters
        public String getPreferenceText() {
            return preferenceText;
        }

        public void setPreferenceText(String preferenceText) {
            this.preferenceText = preferenceText;
        }

        public DietaryPreference.PreferenceType getPreferenceType() {
            return preferenceType;
        }

        public void setPreferenceType(DietaryPreference.PreferenceType preferenceType) {
            this.preferenceType = preferenceType;
        }
    }
}
