package com.prodiet.controller;

import com.prodiet.dto.CreateCustomerRequest;
import com.prodiet.dto.UserResponse;
import com.prodiet.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/customers")
@Tag(name = "Customer Management", description = "Customer management APIs for administrators")
@SecurityRequirement(name = "Bearer Authentication")
public class CustomerController {

    @Autowired
    private UserService userService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get all customers", description = "Retrieve all active customers")
    public ResponseEntity<List<UserResponse>> getAllCustomers() {
        List<UserResponse> customers = userService.getAllCustomers();
        return ResponseEntity.ok(customers);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get customer by ID", description = "Retrieve a specific customer by their ID")
    public ResponseEntity<UserResponse> getCustomerById(
            @Parameter(description = "Customer ID") @PathVariable Long id) {
        UserResponse customer = userService.getCustomerById(id);
        return ResponseEntity.ok(customer);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Create new customer", description = "Create a new customer account")
    public ResponseEntity<UserResponse> createCustomer(@Valid @RequestBody CreateCustomerRequest request) {
        UserResponse createdCustomer = userService.createCustomer(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdCustomer);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Update customer", description = "Update an existing customer's information")
    public ResponseEntity<UserResponse> updateCustomer(
            @Parameter(description = "Customer ID") @PathVariable Long id,
            @Valid @RequestBody CreateCustomerRequest request) {
        UserResponse updatedCustomer = userService.updateCustomer(id, request);
        return ResponseEntity.ok(updatedCustomer);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Delete customer", description = "Soft delete a customer (mark as inactive)")
    public ResponseEntity<Void> deleteCustomer(
            @Parameter(description = "Customer ID") @PathVariable Long id) {
        userService.deleteCustomer(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/count")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get customer count", description = "Get the total number of active customers")
    public ResponseEntity<Map<String, Long>> getCustomerCount() {
        long count = userService.getCustomerCount();
        return ResponseEntity.ok(Map.of("count", count));
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Search customers", description = "Search customers by name or email")
    public ResponseEntity<List<UserResponse>> searchCustomers(
            @Parameter(description = "Search term") @RequestParam String q) {
        List<UserResponse> customers = userService.searchCustomers(q);
        return ResponseEntity.ok(customers);
    }
}
