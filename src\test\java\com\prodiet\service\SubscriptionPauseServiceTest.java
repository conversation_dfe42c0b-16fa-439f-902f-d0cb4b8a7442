package com.prodiet.service;

import com.prodiet.dto.PauseSubscriptionRequest;
import com.prodiet.dto.PauseValidationResponse;
import com.prodiet.dto.PausedDayResponse;
import com.prodiet.entity.*;
import com.prodiet.exception.BusinessLogicException;
import com.prodiet.exception.ResourceNotFoundException;
import com.prodiet.repository.PausedDayRepository;
import com.prodiet.repository.SubscriptionRepository;
import com.prodiet.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SubscriptionPauseServiceTest {

    @Mock
    private SubscriptionRepository subscriptionRepository;

    @Mock
    private PausedDayRepository pausedDayRepository;

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private SubscriptionPauseService subscriptionPauseService;

    private User customer;
    private com.prodiet.entity.Package testPackage;
    private Subscription subscription;

    @BeforeEach
    void setUp() {
        customer = new User("John Doe", "<EMAIL>", "password", User.Role.CUSTOMER);
        customer.setId(1L);
        customer.setIsActive(true);

        testPackage = new com.prodiet.entity.Package("Test Package", com.prodiet.entity.Package.PackageType.STANDARD,
                100.0);
        testPackage.setId(1L);
        testPackage.setDurationDays(30);
        testPackage.setMealsPerDay(3);

        subscription = new Subscription(customer, testPackage, LocalDate.now(), LocalDate.now().plusDays(30));
        subscription.setId(1L);
        subscription.setStatus(Subscription.SubscriptionStatus.ACTIVE);
        subscription.setTotalPausedDays(0);
    }

    @Test
    void testValidatePauseRequest_ValidRequest() {
        LocalDate futureDate = LocalDate.now().plusDays(3);

        when(userRepository.findById(1L)).thenReturn(Optional.of(customer));
        when(subscriptionRepository.findActiveSubscriptionByUserId(1L)).thenReturn(Optional.of(subscription));
        when(pausedDayRepository.existsBySubscriptionIdAndPausedDate(1L, futureDate)).thenReturn(false);

        PauseValidationResponse response = subscriptionPauseService.validatePauseRequest(1L, futureDate);

        assertTrue(response.isCanPause());
        assertEquals("Pause request is valid", response.getMessage());
        assertTrue(response.getViolations().isEmpty());
        assertEquals(7, response.getRemainingPauseDays());
    }

    @Test
    void testValidatePauseRequest_PastDate() {
        LocalDate pastDate = LocalDate.now().minusDays(1);

        when(userRepository.findById(1L)).thenReturn(Optional.of(customer));
        when(subscriptionRepository.findActiveSubscriptionByUserId(1L)).thenReturn(Optional.of(subscription));

        PauseValidationResponse response = subscriptionPauseService.validatePauseRequest(1L, pastDate);

        assertFalse(response.isCanPause());
        assertTrue(response.getViolations().contains("Cannot pause for a date in the past"));
    }

    @Test
    void testValidatePauseRequest_MaxPauseLimitReached() {
        LocalDate futureDate = LocalDate.now().plusDays(3);
        subscription.setTotalPausedDays(7); // Max limit reached

        when(userRepository.findById(1L)).thenReturn(Optional.of(customer));
        when(subscriptionRepository.findActiveSubscriptionByUserId(1L)).thenReturn(Optional.of(subscription));

        PauseValidationResponse response = subscriptionPauseService.validatePauseRequest(1L, futureDate);

        assertFalse(response.isCanPause());
        assertTrue(response.getViolations().contains("Maximum pause limit of 7 days has been reached"));
        assertEquals(0, response.getRemainingPauseDays());
    }

    @Test
    void testValidatePauseRequest_InsufficientAdvanceNotice() {
        LocalDate tomorrowDate = LocalDate.now().plusDays(1);

        when(userRepository.findById(1L)).thenReturn(Optional.of(customer));
        when(subscriptionRepository.findActiveSubscriptionByUserId(1L)).thenReturn(Optional.of(subscription));

        PauseValidationResponse response = subscriptionPauseService.validatePauseRequest(1L, tomorrowDate);

        assertFalse(response.isCanPause());
        assertTrue(response.getViolations().stream()
                .anyMatch(violation -> violation.contains("advance notice")));
    }

    @Test
    void testValidatePauseRequest_AlreadyPaused() {
        LocalDate futureDate = LocalDate.now().plusDays(3);

        when(userRepository.findById(1L)).thenReturn(Optional.of(customer));
        when(subscriptionRepository.findActiveSubscriptionByUserId(1L)).thenReturn(Optional.of(subscription));
        when(pausedDayRepository.existsBySubscriptionIdAndPausedDate(1L, futureDate)).thenReturn(true);

        PauseValidationResponse response = subscriptionPauseService.validatePauseRequest(1L, futureDate);

        assertFalse(response.isCanPause());
        assertTrue(response.getViolations().contains("Subscription is already paused on " + futureDate));
    }

    @Test
    void testPauseSubscription_Success() {
        LocalDate futureDate = LocalDate.now().plusDays(3);
        PauseSubscriptionRequest request = new PauseSubscriptionRequest(futureDate, "Travel");

        when(userRepository.findById(1L)).thenReturn(Optional.of(customer));
        when(subscriptionRepository.findActiveSubscriptionByUserId(1L)).thenReturn(Optional.of(subscription));
        when(pausedDayRepository.existsBySubscriptionIdAndPausedDate(1L, futureDate)).thenReturn(false);
        when(pausedDayRepository.save(any(PausedDay.class))).thenAnswer(invocation -> {
            PausedDay pausedDay = invocation.getArgument(0);
            pausedDay.setId(1L);
            return pausedDay;
        });

        PausedDayResponse response = subscriptionPauseService.pauseSubscription(1L, request);

        assertNotNull(response);
        assertEquals(futureDate, response.getPausedDate());
        assertEquals("Travel", response.getReason());

        verify(pausedDayRepository).save(any(PausedDay.class));
        verify(subscriptionRepository).save(subscription);
        assertEquals(1, subscription.getTotalPausedDays());
    }

    @Test
    void testPauseSubscription_InvalidRequest() {
        LocalDate pastDate = LocalDate.now().minusDays(1);
        PauseSubscriptionRequest request = new PauseSubscriptionRequest(pastDate, "Travel");

        when(userRepository.findById(1L)).thenReturn(Optional.of(customer));
        when(subscriptionRepository.findActiveSubscriptionByUserId(1L)).thenReturn(Optional.of(subscription));

        assertThrows(BusinessLogicException.class, () -> {
            subscriptionPauseService.pauseSubscription(1L, request);
        });
    }

    @Test
    void testPauseSubscription_CustomerNotFound() {
        LocalDate futureDate = LocalDate.now().plusDays(3);
        PauseSubscriptionRequest request = new PauseSubscriptionRequest(futureDate, "Travel");

        when(userRepository.findById(1L)).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
            subscriptionPauseService.pauseSubscription(1L, request);
        });
    }

    @Test
    void testPauseSubscription_NoActiveSubscription() {
        LocalDate futureDate = LocalDate.now().plusDays(3);
        PauseSubscriptionRequest request = new PauseSubscriptionRequest(futureDate, "Travel");

        when(userRepository.findById(1L)).thenReturn(Optional.of(customer));
        when(subscriptionRepository.findActiveSubscriptionByUserId(1L)).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> {
            subscriptionPauseService.pauseSubscription(1L, request);
        });
    }

    @Test
    void testRemovePausedDay_Success() {
        LocalDate futureDate = LocalDate.now().plusDays(3);
        subscription.setTotalPausedDays(1);

        when(userRepository.findById(1L)).thenReturn(Optional.of(customer));
        when(subscriptionRepository.findActiveSubscriptionByUserId(1L)).thenReturn(Optional.of(subscription));
        when(pausedDayRepository.existsBySubscriptionIdAndPausedDate(1L, futureDate)).thenReturn(true);

        subscriptionPauseService.removePausedDay(1L, futureDate);

        verify(pausedDayRepository).deleteBySubscriptionIdAndPausedDate(1L, futureDate);
        verify(subscriptionRepository).save(subscription);
        assertEquals(0, subscription.getTotalPausedDays());
    }

    @Test
    void testRemovePausedDay_PastDate() {
        LocalDate pastDate = LocalDate.now().minusDays(1);

        when(userRepository.findById(1L)).thenReturn(Optional.of(customer));
        when(subscriptionRepository.findActiveSubscriptionByUserId(1L)).thenReturn(Optional.of(subscription));
        when(pausedDayRepository.existsBySubscriptionIdAndPausedDate(1L, pastDate)).thenReturn(true);

        assertThrows(BusinessLogicException.class, () -> {
            subscriptionPauseService.removePausedDay(1L, pastDate);
        });
    }

    @Test
    void testRemovePausedDay_NotFound() {
        LocalDate futureDate = LocalDate.now().plusDays(3);

        when(userRepository.findById(1L)).thenReturn(Optional.of(customer));
        when(subscriptionRepository.findActiveSubscriptionByUserId(1L)).thenReturn(Optional.of(subscription));
        when(pausedDayRepository.existsBySubscriptionIdAndPausedDate(1L, futureDate)).thenReturn(false);

        assertThrows(ResourceNotFoundException.class, () -> {
            subscriptionPauseService.removePausedDay(1L, futureDate);
        });
    }
}
