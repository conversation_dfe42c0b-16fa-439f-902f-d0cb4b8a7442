package com.prodiet.dto;

import java.time.LocalDate;
import java.util.List;

public class PauseValidationResponse {

    private boolean canPause;
    private String message;
    private List<String> violations;
    private Integer remainingPauseDays;
    private LocalDate earliestPauseDate;

    // Constructors
    public PauseValidationResponse() {}

    public PauseValidationResponse(boolean canPause, String message) {
        this.canPause = canPause;
        this.message = message;
    }

    public PauseValidationResponse(boolean canPause, String message, List<String> violations, 
                                 Integer remainingPauseDays, LocalDate earliestPauseDate) {
        this.canPause = canPause;
        this.message = message;
        this.violations = violations;
        this.remainingPauseDays = remainingPauseDays;
        this.earliestPauseDate = earliestPauseDate;
    }

    // Getters and Setters
    public boolean isCanPause() {
        return canPause;
    }

    public void setCanPause(boolean canPause) {
        this.canPause = canPause;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<String> getViolations() {
        return violations;
    }

    public void setViolations(List<String> violations) {
        this.violations = violations;
    }

    public Integer getRemainingPauseDays() {
        return remainingPauseDays;
    }

    public void setRemainingPauseDays(Integer remainingPauseDays) {
        this.remainingPauseDays = remainingPauseDays;
    }

    public LocalDate getEarliestPauseDate() {
        return earliestPauseDate;
    }

    public void setEarliestPauseDate(LocalDate earliestPauseDate) {
        this.earliestPauseDate = earliestPauseDate;
    }
}
