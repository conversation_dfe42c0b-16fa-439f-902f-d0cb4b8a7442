package com.prodiet.service;

import com.prodiet.dto.PackageRequest;
import com.prodiet.dto.PackageResponse;
import com.prodiet.entity.Package;
import com.prodiet.exception.ResourceNotFoundException;
import com.prodiet.exception.DuplicateResourceException;
import com.prodiet.repository.PackageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class PackageService {

    @Autowired
    private PackageRepository packageRepository;

    public List<PackageResponse> getAllPackages() {
        return packageRepository.findByIsActiveTrue()
                .stream()
                .map(PackageResponse::new)
                .collect(Collectors.toList());
    }

    public List<PackageResponse> getPackagesByType(Package.PackageType type) {
        return packageRepository.findByTypeAndIsActiveTrue(type)
                .stream()
                .map(PackageResponse::new)
                .collect(Collectors.toList());
    }

    public PackageResponse getPackageById(Long id) {
        Package pkg = packageRepository.findByIdAndIsActiveTrue(id)
                .orElseThrow(() -> new ResourceNotFoundException("Package not found with id: " + id));
        return new PackageResponse(pkg);
    }

    public PackageResponse createPackage(PackageRequest request) {
        // Check if package name already exists
        if (packageRepository.existsByNameAndIsActiveTrue(request.getName())) {
            throw new DuplicateResourceException("Package with name '" + request.getName() + "' already exists");
        }

        Package pkg = new Package();
        mapRequestToEntity(request, pkg);
        
        Package savedPackage = packageRepository.save(pkg);
        return new PackageResponse(savedPackage);
    }

    public PackageResponse updatePackage(Long id, PackageRequest request) {
        Package pkg = packageRepository.findByIdAndIsActiveTrue(id)
                .orElseThrow(() -> new ResourceNotFoundException("Package not found with id: " + id));

        // Check if new name conflicts with existing packages (excluding current package)
        if (!pkg.getName().equals(request.getName()) && 
            packageRepository.existsByNameAndIsActiveTrue(request.getName())) {
            throw new DuplicateResourceException("Package with name '" + request.getName() + "' already exists");
        }

        mapRequestToEntity(request, pkg);
        
        Package updatedPackage = packageRepository.save(pkg);
        return new PackageResponse(updatedPackage);
    }

    public void deletePackage(Long id) {
        Package pkg = packageRepository.findByIdAndIsActiveTrue(id)
                .orElseThrow(() -> new ResourceNotFoundException("Package not found with id: " + id));
        
        // Soft delete
        pkg.setIsActive(false);
        packageRepository.save(pkg);
    }

    private void mapRequestToEntity(PackageRequest request, Package pkg) {
        pkg.setName(request.getName());
        pkg.setDescription(request.getDescription());
        pkg.setType(request.getType());
        pkg.setPrice(request.getPrice());

        if (request.getType() == Package.PackageType.STANDARD) {
            pkg.setDurationDays(request.getDurationDays());
            pkg.setDailyProteinG(request.getDailyProteinG());
            pkg.setDailyCarbsG(request.getDailyCarbsG());
            pkg.setMealsPerDay(request.getMealsPerDay());
            
            // Clear add-on specific fields
            pkg.setMealsPerWeek(null);
            pkg.setFrequencyPerWeek(null);
            pkg.setProductionDays(null);
        } else if (request.getType() == Package.PackageType.ADD_ON) {
            pkg.setMealsPerWeek(request.getMealsPerWeek());
            pkg.setFrequencyPerWeek(request.getFrequencyPerWeek());
            pkg.setProductionDays(request.getProductionDays());
            
            // Clear standard specific fields
            pkg.setDurationDays(null);
            pkg.setDailyProteinG(null);
            pkg.setDailyCarbsG(null);
            pkg.setMealsPerDay(null);
        }
    }
}
