package com.prodiet.service;

import com.prodiet.dto.ProductionReportDto;
import com.prodiet.entity.*;
import com.prodiet.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProductionServiceTest {

        @Mock
        private SubscriptionRepository subscriptionRepository;

        @Mock
        private MealRepository mealRepository;

        @Mock
        private MealComponentRepository mealComponentRepository;

        @Mock
        private MealExclusionRepository mealExclusionRepository;

        @Mock
        private FridayService fridayService;

        @InjectMocks
        private ProductionService productionService;

        private User customer1, customer2;
        private com.prodiet.entity.Package testPackage;
        private Subscription subscription1, subscription2;
        private Meal meal1, meal2;
        private MealComponent component1, component2, component3;

        @BeforeEach
        void setUp() {
                // Create customers
                customer1 = new User("John Doe", "<EMAIL>", "password", User.Role.CUSTOMER);
                customer1.setId(1L);

                customer2 = new User("Jane Smith", "<EMAIL>", "password", User.Role.CUSTOMER);
                customer2.setId(2L);

                // Create package
                testPackage = new com.prodiet.entity.Package("Test Package",
                                com.prodiet.entity.Package.PackageType.STANDARD, 100.0);
                testPackage.setId(1L);
                testPackage.setDurationDays(30);
                testPackage.setMealsPerDay(3);

                // Create subscriptions
                subscription1 = new Subscription(customer1, testPackage,
                                LocalDate.now().minusDays(5), LocalDate.now().plusDays(25));
                subscription1.setId(1L);
                subscription1.setStatus(Subscription.SubscriptionStatus.ACTIVE);
                subscription1.setFridayPreference(Subscription.FridayPreference.RECEIVE_ON_THURSDAY);

                subscription2 = new Subscription(customer2, testPackage,
                                LocalDate.now().minusDays(3), LocalDate.now().plusDays(27));
                subscription2.setId(2L);
                subscription2.setStatus(Subscription.SubscriptionStatus.ACTIVE);
                subscription2.setFridayPreference(Subscription.FridayPreference.SKIP_AND_EXTEND);

                // Create meals
                meal1 = new Meal("Grilled Chicken", "Healthy protein meal", Meal.MealType.LUNCH);
                meal1.setId(1L);

                meal2 = new Meal("Vegetable Salad", "Fresh vegetable salad", Meal.MealType.DINNER);
                meal2.setId(2L);

                // Create meal components
                component1 = new MealComponent("Chicken Breast", 150, meal1);
                component1.setId(1L);

                component2 = new MealComponent("Rice", 100, meal1);
                component2.setId(2L);

                component3 = new MealComponent("Mixed Vegetables", 80, meal2);
                component3.setId(3L);
        }

        @Test
        void testGenerateProductionReport_RegularDay() {
                LocalDate testDate = LocalDate.now();
                List<Subscription> subscriptions = List.of(subscription1, subscription2);
                List<Meal> meals = List.of(meal1, meal2);

                when(subscriptionRepository.findActiveSubscriptionsForDate(testDate)).thenReturn(subscriptions);
                when(fridayService.shouldReceiveMealsOnDate(subscription1, testDate)).thenReturn(true);
                when(fridayService.shouldReceiveMealsOnDate(subscription2, testDate)).thenReturn(true);
                when(fridayService.shouldReceiveFridayMealsOnThursday(subscription1, testDate)).thenReturn(false);
                when(fridayService.shouldReceiveFridayMealsOnThursday(subscription2, testDate)).thenReturn(false);
                when(fridayService.getMealMultiplier(subscription1, testDate)).thenReturn(1);
                when(fridayService.getMealMultiplier(subscription2, testDate)).thenReturn(1);

                when(mealRepository.findByPackageIdAndIsActiveTrue(1L)).thenReturn(meals);
                when(mealExclusionRepository.findBySubscriptionId(1L)).thenReturn(new ArrayList<>());
                when(mealExclusionRepository.findBySubscriptionId(2L)).thenReturn(new ArrayList<>());

                when(mealComponentRepository.findByMealId(1L)).thenReturn(List.of(component1, component2));
                when(mealComponentRepository.findByMealId(2L)).thenReturn(List.of(component3));

                ProductionReportDto report = productionService.generateProductionReport(testDate);

                assertNotNull(report);
                assertEquals(testDate, report.getReportDate());
                assertEquals(2, report.getTotalActiveSubscriptions());
                assertEquals(4, report.getTotalMealsToDeliver()); // 2 customers * 2 meals each

                // Check customer breakdown
                assertEquals(2, report.getCustomerBreakdown().size());

                // Check ingredient summary
                assertNotNull(report.getIngredientSummary());
                assertTrue(report.getIngredientSummary().size() > 0);

                // Verify ingredient totals
                Map<String, Integer> ingredientTotals = report.getIngredientSummary().stream()
                                .collect(java.util.stream.Collectors.toMap(
                                                ProductionReportDto.IngredientSummaryDto::getIngredientName,
                                                ProductionReportDto.IngredientSummaryDto::getTotalWeightG));

                assertEquals(300, ingredientTotals.get("Chicken Breast")); // 150g * 2 customers
                assertEquals(200, ingredientTotals.get("Rice")); // 100g * 2 customers
                assertEquals(160, ingredientTotals.get("Mixed Vegetables")); // 80g * 2 customers
        }

        @Test
        void testGenerateProductionReport_ThursdayWithFridayPreference() {
                LocalDate testDate = LocalDate.now();
                List<Subscription> subscriptions = List.of(subscription1, subscription2);
                List<Meal> meals = List.of(meal1, meal2);

                when(subscriptionRepository.findActiveSubscriptionsForDate(testDate)).thenReturn(subscriptions);
                when(fridayService.shouldReceiveMealsOnDate(subscription1, testDate)).thenReturn(true);
                when(fridayService.shouldReceiveMealsOnDate(subscription2, testDate)).thenReturn(true);
                when(fridayService.shouldReceiveFridayMealsOnThursday(subscription1, testDate)).thenReturn(true);
                when(fridayService.shouldReceiveFridayMealsOnThursday(subscription2, testDate)).thenReturn(false);
                when(fridayService.getMealMultiplier(subscription1, testDate)).thenReturn(2); // Double for Thursday +
                                                                                              // Friday
                when(fridayService.getMealMultiplier(subscription2, testDate)).thenReturn(1); // Normal

                when(mealRepository.findByPackageIdAndIsActiveTrue(1L)).thenReturn(meals);
                when(mealExclusionRepository.findBySubscriptionId(1L)).thenReturn(new ArrayList<>());
                when(mealExclusionRepository.findBySubscriptionId(2L)).thenReturn(new ArrayList<>());

                when(mealComponentRepository.findByMealId(1L)).thenReturn(List.of(component1, component2));
                when(mealComponentRepository.findByMealId(2L)).thenReturn(List.of(component3));

                ProductionReportDto report = productionService.generateProductionReport(testDate);

                assertNotNull(report);
                assertEquals(2, report.getTotalActiveSubscriptions());
                assertEquals(6, report.getTotalMealsToDeliver()); // Customer1: 2*2=4, Customer2: 2*1=2

                // Check customer breakdown
                assertEquals(2, report.getCustomerBreakdown().size());

                ProductionReportDto.CustomerMealDto customer1Data = report.getCustomerBreakdown().stream()
                                .filter(c -> c.getCustomerId().equals(1L))
                                .findFirst()
                                .orElse(null);

                assertNotNull(customer1Data);
                assertEquals(2, customer1Data.getMealMultiplier());
                assertEquals("RECEIVE_ON_THURSDAY", customer1Data.getFridayPreference());

                ProductionReportDto.CustomerMealDto customer2Data = report.getCustomerBreakdown().stream()
                                .filter(c -> c.getCustomerId().equals(2L))
                                .findFirst()
                                .orElse(null);

                assertNotNull(customer2Data);
                assertEquals(1, customer2Data.getMealMultiplier());
                assertEquals("SKIP_AND_EXTEND", customer2Data.getFridayPreference());
        }

        @Test
        void testGenerateProductionReport_WithMealExclusions() {
                LocalDate testDate = LocalDate.now();
                List<Subscription> subscriptions = List.of(subscription1);
                List<Meal> meals = List.of(meal1, meal2);

                // Customer1 excludes meal2
                MealExclusion exclusion = new MealExclusion(subscription1, meal2);

                when(subscriptionRepository.findActiveSubscriptionsForDate(testDate)).thenReturn(subscriptions);
                when(fridayService.shouldReceiveMealsOnDate(subscription1, testDate)).thenReturn(true);
                when(fridayService.shouldReceiveFridayMealsOnThursday(subscription1, testDate)).thenReturn(false);
                when(fridayService.getMealMultiplier(subscription1, testDate)).thenReturn(1);

                when(mealRepository.findByPackageIdAndIsActiveTrue(1L)).thenReturn(meals);
                when(mealExclusionRepository.findBySubscriptionId(1L)).thenReturn(List.of(exclusion));

                when(mealComponentRepository.findByMealId(1L)).thenReturn(List.of(component1, component2));

                ProductionReportDto report = productionService.generateProductionReport(testDate);

                assertNotNull(report);
                assertEquals(1, report.getTotalActiveSubscriptions());
                assertEquals(1, report.getTotalMealsToDeliver()); // Only meal1, meal2 is excluded

                // Check customer breakdown
                assertEquals(1, report.getCustomerBreakdown().size());
                ProductionReportDto.CustomerMealDto customerData = report.getCustomerBreakdown().get(0);
                assertEquals(1, customerData.getMeals().size()); // Only meal1
                assertEquals("Grilled Chicken", customerData.getMeals().get(0).getMealName());
        }

        @Test
        void testGetActiveSubscriptionsForDate() {
                LocalDate testDate = LocalDate.now();
                List<Subscription> allSubscriptions = List.of(subscription1, subscription2);

                when(subscriptionRepository.findActiveSubscriptionsForDate(testDate)).thenReturn(allSubscriptions);
                when(fridayService.shouldReceiveMealsOnDate(subscription1, testDate)).thenReturn(true);
                when(fridayService.shouldReceiveMealsOnDate(subscription2, testDate)).thenReturn(false);
                when(fridayService.shouldReceiveFridayMealsOnThursday(subscription1, testDate)).thenReturn(false);
                when(fridayService.shouldReceiveFridayMealsOnThursday(subscription2, testDate)).thenReturn(false);

                List<Subscription> activeSubscriptions = productionService.getActiveSubscriptionsForDate(testDate);

                assertEquals(1, activeSubscriptions.size());
                assertEquals(subscription1.getId(), activeSubscriptions.get(0).getId());
        }

        @Test
        void testGetProductionStats() {
                LocalDate startDate = LocalDate.now();
                LocalDate endDate = startDate.plusDays(2);

                when(subscriptionRepository.findActiveSubscriptionsForDate(any(LocalDate.class)))
                                .thenReturn(List.of(subscription1, subscription2));
                when(fridayService.shouldReceiveMealsOnDate(any(Subscription.class), any(LocalDate.class)))
                                .thenReturn(true);
                when(fridayService.shouldReceiveFridayMealsOnThursday(any(Subscription.class), any(LocalDate.class)))
                                .thenReturn(false);
                when(fridayService.getEffectiveMealCount(any(Subscription.class), any(LocalDate.class)))
                                .thenReturn(3);

                Map<String, Object> stats = productionService.getProductionStats(startDate, endDate);

                assertNotNull(stats);
                assertEquals(3, stats.get("totalDays"));
                assertEquals(6, stats.get("totalSubscriptions")); // 2 subscriptions * 3 days
                assertEquals(18, stats.get("totalMeals")); // 2 subscriptions * 3 meals * 3 days
                assertEquals(2.0, stats.get("averageSubscriptionsPerDay"));
                assertEquals(6.0, stats.get("averageMealsPerDay"));
        }

        @Test
        void testGetMostUsedIngredients() {
                LocalDate testDate = LocalDate.now();
                List<Subscription> subscriptions = List.of(subscription1, subscription2);
                List<Meal> meals = List.of(meal1, meal2);

                when(subscriptionRepository.findActiveSubscriptionsForDate(testDate)).thenReturn(subscriptions);
                when(fridayService.shouldReceiveMealsOnDate(any(Subscription.class), any(LocalDate.class)))
                                .thenReturn(true);
                when(fridayService.shouldReceiveFridayMealsOnThursday(any(Subscription.class), any(LocalDate.class)))
                                .thenReturn(false);
                when(fridayService.getMealMultiplier(any(Subscription.class), any(LocalDate.class)))
                                .thenReturn(1);

                when(mealRepository.findByPackageIdAndIsActiveTrue(1L)).thenReturn(meals);
                when(mealExclusionRepository.findBySubscriptionId(anyLong())).thenReturn(new ArrayList<>());

                when(mealComponentRepository.findByMealId(1L)).thenReturn(List.of(component1, component2));
                when(mealComponentRepository.findByMealId(2L)).thenReturn(List.of(component3));

                List<ProductionReportDto.IngredientSummaryDto> mostUsed = productionService.getMostUsedIngredients(2);

                assertNotNull(mostUsed);
                assertEquals(2, mostUsed.size());

                // Should be sorted by weight descending
                assertTrue(mostUsed.get(0).getTotalWeightG() >= mostUsed.get(1).getTotalWeightG());
        }
}
