# Test Application Configuration
spring.application.name=prodiet-backend-test

# Test Database Configuration (In-Memory SQLite)
spring.datasource.url=************************
spring.datasource.driver-class-name=org.sqlite.JDBC
spring.datasource.username=
spring.datasource.password=

# JPA/Hibernate Configuration for Tests
spring.jpa.database-platform=org.hibernate.community.dialect.SQLiteDialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# JWT Configuration for Tests
jwt.secret=testSecretKey123456789012345678901234567890
jwt.expiration=3600000

# Logging Configuration for Tests
logging.level.com.prodiet=WARN
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.org.springframework.web=WARN

# Disable Swagger in tests
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false
