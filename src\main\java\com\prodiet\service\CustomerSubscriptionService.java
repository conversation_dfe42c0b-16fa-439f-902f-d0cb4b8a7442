package com.prodiet.service;

import com.prodiet.dto.CustomerSubscriptionResponse;
import com.prodiet.dto.PausedDayResponse;
import com.prodiet.entity.Subscription;
import com.prodiet.entity.User;
import com.prodiet.exception.ResourceNotFoundException;
import com.prodiet.repository.PausedDayRepository;
import com.prodiet.repository.SubscriptionRepository;
import com.prodiet.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = true)
public class CustomerSubscriptionService {

    @Autowired
    private SubscriptionRepository subscriptionRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PausedDayRepository pausedDayRepository;

    public CustomerSubscriptionResponse getCustomerSubscription(Long customerId) {
        // Verify customer exists and is active
        User customer = userRepository.findById(customerId)
                .orElseThrow(() -> new ResourceNotFoundException("Customer not found with id: " + customerId));

        if (customer.getRole() != User.Role.CUSTOMER || !customer.getIsActive()) {
            throw new ResourceNotFoundException("Customer not found with id: " + customerId);
        }

        // Get active subscription
        Subscription subscription = subscriptionRepository.findActiveSubscriptionByUserId(customerId)
                .orElseThrow(() -> new ResourceNotFoundException("No active subscription found for customer"));

        return new CustomerSubscriptionResponse(subscription);
    }

    public List<PausedDayResponse> getSubscriptionPausedDays(Long subscriptionId) {
        // Verify subscription exists
        Subscription subscription = subscriptionRepository.findById(subscriptionId)
                .orElseThrow(() -> new ResourceNotFoundException("Subscription not found with id: " + subscriptionId));

        // Get paused days ordered by date
        return pausedDayRepository.findBySubscriptionIdOrderByPausedDateAsc(subscriptionId)
                .stream()
                .map(PausedDayResponse::new)
                .collect(Collectors.toList());
    }

    public List<PausedDayResponse> getCustomerPausedDays(Long customerId) {
        // Verify customer exists and is active
        User customer = userRepository.findById(customerId)
                .orElseThrow(() -> new ResourceNotFoundException("Customer not found with id: " + customerId));

        if (customer.getRole() != User.Role.CUSTOMER || !customer.getIsActive()) {
            throw new ResourceNotFoundException("Customer not found with id: " + customerId);
        }

        // Get active subscription
        Subscription subscription = subscriptionRepository.findActiveSubscriptionByUserId(customerId)
                .orElseThrow(() -> new ResourceNotFoundException("No active subscription found for customer"));

        // Get paused days for the subscription
        return getSubscriptionPausedDays(subscription.getId());
    }

    public boolean hasCustomerActiveSubscription(Long customerId) {
        return subscriptionRepository.findActiveSubscriptionByUserId(customerId).isPresent();
    }

    public Long getCustomerActiveSubscriptionId(Long customerId) {
        return subscriptionRepository.findActiveSubscriptionByUserId(customerId)
                .map(Subscription::getId)
                .orElse(null);
    }

    public boolean isCustomerOwnerOfSubscription(Long customerId, Long subscriptionId) {
        return subscriptionRepository.findById(subscriptionId)
                .map(subscription -> subscription.getUser().getId().equals(customerId))
                .orElse(false);
    }
}
