package com.prodiet.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "paused_days")
public class PausedDay {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "Paused date is required")
    @Column(name = "paused_date", nullable = false)
    private LocalDate pausedDate;

    @Column(name = "reason", length = 200)
    private String reason;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subscription_id", nullable = false)
    private Subscription subscription;

    // Constructors
    public PausedDay() {
        this.createdAt = LocalDateTime.now();
    }

    public PausedDay(LocalDate pausedDate, Subscription subscription) {
        this();
        this.pausedDate = pausedDate;
        this.subscription = subscription;
    }

    public PausedDay(LocalDate pausedDate, String reason, Subscription subscription) {
        this(pausedDate, subscription);
        this.reason = reason;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getPausedDate() {
        return pausedDate;
    }

    public void setPausedDate(LocalDate pausedDate) {
        this.pausedDate = pausedDate;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public Subscription getSubscription() {
        return subscription;
    }

    public void setSubscription(Subscription subscription) {
        this.subscription = subscription;
    }
}
