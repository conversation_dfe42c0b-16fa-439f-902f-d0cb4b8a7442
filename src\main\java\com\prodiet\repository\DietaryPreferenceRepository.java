package com.prodiet.repository;

import com.prodiet.entity.DietaryPreference;
import com.prodiet.entity.Subscription;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DietaryPreferenceRepository extends JpaRepository<DietaryPreference, Long> {

    List<DietaryPreference> findBySubscription(Subscription subscription);

    List<DietaryPreference> findBySubscriptionId(Long subscriptionId);

    void deleteBySubscriptionId(Long subscriptionId);

    List<DietaryPreference> findByPreferenceType(DietaryPreference.PreferenceType preferenceType);

    List<DietaryPreference> findBySubscriptionIdAndPreferenceType(Long subscriptionId, DietaryPreference.PreferenceType preferenceType);
}
