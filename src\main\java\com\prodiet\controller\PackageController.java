package com.prodiet.controller;

import com.prodiet.dto.PackageRequest;
import com.prodiet.dto.PackageResponse;
import com.prodiet.entity.Package;
import com.prodiet.service.PackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/packages")
@Tag(name = "Package Management", description = "Package management APIs for administrators")
@SecurityRequirement(name = "Bearer Authentication")
public class PackageController {

    @Autowired
    private PackageService packageService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get all packages", description = "Retrieve all active packages")
    public ResponseEntity<List<PackageResponse>> getAllPackages() {
        List<PackageResponse> packages = packageService.getAllPackages();
        return ResponseEntity.ok(packages);
    }

    @GetMapping("/type/{type}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get packages by type", description = "Retrieve packages filtered by type (STANDARD or ADD_ON)")
    public ResponseEntity<List<PackageResponse>> getPackagesByType(
            @Parameter(description = "Package type") @PathVariable Package.PackageType type) {
        List<PackageResponse> packages = packageService.getPackagesByType(type);
        return ResponseEntity.ok(packages);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get package by ID", description = "Retrieve a specific package by its ID")
    public ResponseEntity<PackageResponse> getPackageById(
            @Parameter(description = "Package ID") @PathVariable Long id) {
        PackageResponse packageResponse = packageService.getPackageById(id);
        return ResponseEntity.ok(packageResponse);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Create new package", description = "Create a new meal package")
    public ResponseEntity<PackageResponse> createPackage(@Valid @RequestBody PackageRequest request) {
        PackageResponse createdPackage = packageService.createPackage(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdPackage);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Update package", description = "Update an existing package")
    public ResponseEntity<PackageResponse> updatePackage(
            @Parameter(description = "Package ID") @PathVariable Long id,
            @Valid @RequestBody PackageRequest request) {
        PackageResponse updatedPackage = packageService.updatePackage(id, request);
        return ResponseEntity.ok(updatedPackage);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Delete package", description = "Soft delete a package (mark as inactive)")
    public ResponseEntity<Void> deletePackage(
            @Parameter(description = "Package ID") @PathVariable Long id) {
        packageService.deletePackage(id);
        return ResponseEntity.noContent().build();
    }
}
