package com.prodiet.repository;

import com.prodiet.entity.Subscription;
import com.prodiet.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface SubscriptionRepository extends JpaRepository<Subscription, Long> {

    List<Subscription> findByUser(User user);

    List<Subscription> findByUserId(Long userId);

    Optional<Subscription> findByUserAndStatus(User user, Subscription.SubscriptionStatus status);

    List<Subscription> findByStatus(Subscription.SubscriptionStatus status);

    @Query("SELECT s FROM Subscription s WHERE s.status = :status AND s.startDate <= :date AND s.endDate >= :date")
    List<Subscription> findActiveSubscriptionsForDate(@Param("status") Subscription.SubscriptionStatus status, 
                                                      @Param("date") LocalDate date);

    @Query("SELECT s FROM Subscription s WHERE s.status = 'ACTIVE' AND s.startDate <= :date AND s.endDate >= :date")
    List<Subscription> findActiveSubscriptionsForDate(@Param("date") LocalDate date);

    @Query("SELECT s FROM Subscription s WHERE s.user.id = :userId AND s.status IN ('ACTIVE', 'PAUSED')")
    Optional<Subscription> findActiveSubscriptionByUserId(@Param("userId") Long userId);

    @Query("SELECT COUNT(s) FROM Subscription s WHERE s.status = :status")
    long countByStatus(@Param("status") Subscription.SubscriptionStatus status);

    @Query("SELECT s FROM Subscription s WHERE s.endDate < :date AND s.status = 'ACTIVE'")
    List<Subscription> findExpiredActiveSubscriptions(@Param("date") LocalDate date);

    @Query("SELECT s FROM Subscription s JOIN s.pausedDays pd WHERE pd.pausedDate = :date")
    List<Subscription> findSubscriptionsWithPausedDate(@Param("date") LocalDate date);
}
