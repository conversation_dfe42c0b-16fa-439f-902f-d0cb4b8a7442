package com.prodiet.repository;

import com.prodiet.entity.MealExclusion;
import com.prodiet.entity.Subscription;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MealExclusionRepository extends JpaRepository<MealExclusion, Long> {

    List<MealExclusion> findBySubscription(Subscription subscription);

    List<MealExclusion> findBySubscriptionId(Long subscriptionId);

    void deleteBySubscriptionId(Long subscriptionId);

    void deleteBySubscriptionIdAndMealId(Long subscriptionId, Long mealId);

    boolean existsBySubscriptionIdAndMealId(Long subscriptionId, Long mealId);
}
