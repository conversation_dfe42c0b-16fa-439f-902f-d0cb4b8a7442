package com.prodiet.service;

import com.prodiet.dto.CreateCustomerRequest;
import com.prodiet.dto.UserResponse;
import com.prodiet.entity.User;
import com.prodiet.exception.DuplicateResourceException;
import com.prodiet.exception.ResourceNotFoundException;
import com.prodiet.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    public List<UserResponse> getAllCustomers() {
        return userRepository.findByRoleAndIsActiveTrue(User.Role.CUSTOMER)
                .stream()
                .map(UserResponse::new)
                .collect(Collectors.toList());
    }

    public UserResponse getCustomerById(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Customer not found with id: " + id));
        
        if (user.getRole() != User.Role.CUSTOMER || !user.getIsActive()) {
            throw new ResourceNotFoundException("Customer not found with id: " + id);
        }
        
        return new UserResponse(user);
    }

    public UserResponse createCustomer(CreateCustomerRequest request) {
        // Check if email already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new DuplicateResourceException("User with email '" + request.getEmail() + "' already exists");
        }

        User customer = new User();
        customer.setName(request.getName());
        customer.setEmail(request.getEmail());
        customer.setPassword(passwordEncoder.encode(request.getPassword()));
        customer.setRole(User.Role.CUSTOMER);
        customer.setPhoneNumber(request.getPhoneNumber());
        customer.setAddress(request.getAddress());
        customer.setIsActive(true);

        User savedCustomer = userRepository.save(customer);
        return new UserResponse(savedCustomer);
    }

    public UserResponse updateCustomer(Long id, CreateCustomerRequest request) {
        User customer = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Customer not found with id: " + id));

        if (customer.getRole() != User.Role.CUSTOMER || !customer.getIsActive()) {
            throw new ResourceNotFoundException("Customer not found with id: " + id);
        }

        // Check if new email conflicts with existing users (excluding current user)
        if (!customer.getEmail().equals(request.getEmail()) && 
            userRepository.existsByEmail(request.getEmail())) {
            throw new DuplicateResourceException("User with email '" + request.getEmail() + "' already exists");
        }

        customer.setName(request.getName());
        customer.setEmail(request.getEmail());
        
        // Only update password if provided
        if (request.getPassword() != null && !request.getPassword().trim().isEmpty()) {
            customer.setPassword(passwordEncoder.encode(request.getPassword()));
        }
        
        customer.setPhoneNumber(request.getPhoneNumber());
        customer.setAddress(request.getAddress());

        User updatedCustomer = userRepository.save(customer);
        return new UserResponse(updatedCustomer);
    }

    public void deleteCustomer(Long id) {
        User customer = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Customer not found with id: " + id));

        if (customer.getRole() != User.Role.CUSTOMER || !customer.getIsActive()) {
            throw new ResourceNotFoundException("Customer not found with id: " + id);
        }

        // Soft delete
        customer.setIsActive(false);
        userRepository.save(customer);
    }

    public long getCustomerCount() {
        return userRepository.countActiveUsersByRole(User.Role.CUSTOMER);
    }

    public List<UserResponse> searchCustomers(String searchTerm) {
        // This is a simple implementation - you might want to add more sophisticated search
        return userRepository.findByRoleAndIsActiveTrue(User.Role.CUSTOMER)
                .stream()
                .filter(user -> user.getName().toLowerCase().contains(searchTerm.toLowerCase()) ||
                               user.getEmail().toLowerCase().contains(searchTerm.toLowerCase()))
                .map(UserResponse::new)
                .collect(Collectors.toList());
    }
}
