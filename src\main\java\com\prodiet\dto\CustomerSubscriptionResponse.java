package com.prodiet.dto;

import com.prodiet.entity.Subscription;

import java.time.LocalDate;

public class CustomerSubscriptionResponse {

    private Long id;
    private String packageName;
    private String packageDescription;
    private LocalDate startDate;
    private LocalDate endDate;
    private String status;
    private String fridayPreference;
    private String deliveryMethod;
    private String deliveryAddress;
    private String deliveryNotes;
    private Integer totalPausedDays;
    private Integer remainingPauseDays;
    private PackageDetailsDto packageDetails;

    // Constructors
    public CustomerSubscriptionResponse() {}

    public CustomerSubscriptionResponse(Subscription subscription) {
        this.id = subscription.getId();
        this.packageName = subscription.getSubscriptionPackage().getName();
        this.packageDescription = subscription.getSubscriptionPackage().getDescription();
        this.startDate = subscription.getStartDate();
        this.endDate = subscription.getEndDate();
        this.status = subscription.getStatus().name();
        this.fridayPreference = subscription.getFridayPreference().name();
        this.deliveryMethod = subscription.getDeliveryMethod().name();
        this.deliveryAddress = subscription.getDeliveryAddress();
        this.deliveryNotes = subscription.getDeliveryNotes();
        this.totalPausedDays = subscription.getTotalPausedDays();
        this.remainingPauseDays = Math.max(0, 7 - subscription.getTotalPausedDays());
        this.packageDetails = new PackageDetailsDto(subscription.getSubscriptionPackage());
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getPackageDescription() {
        return packageDescription;
    }

    public void setPackageDescription(String packageDescription) {
        this.packageDescription = packageDescription;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFridayPreference() {
        return fridayPreference;
    }

    public void setFridayPreference(String fridayPreference) {
        this.fridayPreference = fridayPreference;
    }

    public String getDeliveryMethod() {
        return deliveryMethod;
    }

    public void setDeliveryMethod(String deliveryMethod) {
        this.deliveryMethod = deliveryMethod;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public String getDeliveryNotes() {
        return deliveryNotes;
    }

    public void setDeliveryNotes(String deliveryNotes) {
        this.deliveryNotes = deliveryNotes;
    }

    public Integer getTotalPausedDays() {
        return totalPausedDays;
    }

    public void setTotalPausedDays(Integer totalPausedDays) {
        this.totalPausedDays = totalPausedDays;
    }

    public Integer getRemainingPauseDays() {
        return remainingPauseDays;
    }

    public void setRemainingPauseDays(Integer remainingPauseDays) {
        this.remainingPauseDays = remainingPauseDays;
    }

    public PackageDetailsDto getPackageDetails() {
        return packageDetails;
    }

    public void setPackageDetails(PackageDetailsDto packageDetails) {
        this.packageDetails = packageDetails;
    }

    // Inner DTO class for package details
    public static class PackageDetailsDto {
        private Long id;
        private String type;
        private Integer durationDays;
        private Integer dailyProteinG;
        private Integer dailyCarbsG;
        private Integer mealsPerDay;
        private Double price;

        public PackageDetailsDto() {}

        public PackageDetailsDto(com.prodiet.entity.Package pkg) {
            this.id = pkg.getId();
            this.type = pkg.getType().name();
            this.durationDays = pkg.getDurationDays();
            this.dailyProteinG = pkg.getDailyProteinG();
            this.dailyCarbsG = pkg.getDailyCarbsG();
            this.mealsPerDay = pkg.getMealsPerDay();
            this.price = pkg.getPrice();
        }

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Integer getDurationDays() {
            return durationDays;
        }

        public void setDurationDays(Integer durationDays) {
            this.durationDays = durationDays;
        }

        public Integer getDailyProteinG() {
            return dailyProteinG;
        }

        public void setDailyProteinG(Integer dailyProteinG) {
            this.dailyProteinG = dailyProteinG;
        }

        public Integer getDailyCarbsG() {
            return dailyCarbsG;
        }

        public void setDailyCarbsG(Integer dailyCarbsG) {
            this.dailyCarbsG = dailyCarbsG;
        }

        public Integer getMealsPerDay() {
            return mealsPerDay;
        }

        public void setMealsPerDay(Integer mealsPerDay) {
            this.mealsPerDay = mealsPerDay;
        }

        public Double getPrice() {
            return price;
        }

        public void setPrice(Double price) {
            this.price = price;
        }
    }
}
