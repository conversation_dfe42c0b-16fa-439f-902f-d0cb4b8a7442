package com.prodiet.controller;

import com.prodiet.dto.ProductionReportDto;
import com.prodiet.service.ProductionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/production")
@Tag(name = "Production Reports", description = "Production reporting APIs for administrators")
@SecurityRequirement(name = "Bearer Authentication")
public class ProductionController {

    @Autowired
    private ProductionService productionService;

    @GetMapping("/today/aggregated")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get today's aggregated production report", 
               description = "Get aggregated ingredient totals for today's production")
    public ResponseEntity<List<ProductionReportDto.IngredientSummaryDto>> getTodayAggregatedReport() {
        LocalDate today = LocalDate.now();
        ProductionReportDto report = productionService.generateProductionReport(today);
        return ResponseEntity.ok(report.getIngredientSummary());
    }

    @GetMapping("/date/{date}/aggregated")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get aggregated production report for specific date", 
               description = "Get aggregated ingredient totals for a specific date")
    public ResponseEntity<List<ProductionReportDto.IngredientSummaryDto>> getAggregatedReportForDate(
            @Parameter(description = "Date (YYYY-MM-DD)") 
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        ProductionReportDto report = productionService.generateProductionReport(date);
        return ResponseEntity.ok(report.getIngredientSummary());
    }

    @GetMapping("/today/customer-breakdown")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get today's customer breakdown report", 
               description = "Get detailed meal-by-meal, component-by-component breakdown for each customer")
    public ResponseEntity<List<ProductionReportDto.CustomerMealDto>> getTodayCustomerBreakdown() {
        LocalDate today = LocalDate.now();
        ProductionReportDto report = productionService.generateProductionReport(today);
        return ResponseEntity.ok(report.getCustomerBreakdown());
    }

    @GetMapping("/date/{date}/customer-breakdown")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get customer breakdown report for specific date", 
               description = "Get detailed meal-by-meal, component-by-component breakdown for a specific date")
    public ResponseEntity<List<ProductionReportDto.CustomerMealDto>> getCustomerBreakdownForDate(
            @Parameter(description = "Date (YYYY-MM-DD)") 
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        ProductionReportDto report = productionService.generateProductionReport(date);
        return ResponseEntity.ok(report.getCustomerBreakdown());
    }

    @GetMapping("/today/full-report")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get today's complete production report", 
               description = "Get complete production report including aggregated ingredients and customer breakdown")
    public ResponseEntity<ProductionReportDto> getTodayFullReport() {
        LocalDate today = LocalDate.now();
        ProductionReportDto report = productionService.generateProductionReport(today);
        return ResponseEntity.ok(report);
    }

    @GetMapping("/date/{date}/full-report")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get complete production report for specific date", 
               description = "Get complete production report for a specific date")
    public ResponseEntity<ProductionReportDto> getFullReportForDate(
            @Parameter(description = "Date (YYYY-MM-DD)") 
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        ProductionReportDto report = productionService.generateProductionReport(date);
        return ResponseEntity.ok(report);
    }

    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get production statistics", 
               description = "Get production statistics for a date range")
    public ResponseEntity<Map<String, Object>> getProductionStats(
            @Parameter(description = "Start date (YYYY-MM-DD)") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "End date (YYYY-MM-DD)") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        Map<String, Object> stats = productionService.getProductionStats(startDate, endDate);
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/ingredients/most-used")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get most used ingredients", 
               description = "Get the most frequently used ingredients across all active subscriptions")
    public ResponseEntity<List<ProductionReportDto.IngredientSummaryDto>> getMostUsedIngredients(
            @Parameter(description = "Number of ingredients to return") 
            @RequestParam(defaultValue = "10") int limit) {
        List<ProductionReportDto.IngredientSummaryDto> ingredients = productionService.getMostUsedIngredients(limit);
        return ResponseEntity.ok(ingredients);
    }

    @GetMapping("/summary")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get production summary", 
               description = "Get a quick summary of today's production requirements")
    public ResponseEntity<Map<String, Object>> getProductionSummary() {
        LocalDate today = LocalDate.now();
        ProductionReportDto report = productionService.generateProductionReport(today);
        
        Map<String, Object> summary = Map.of(
            "date", today,
            "totalActiveSubscriptions", report.getTotalActiveSubscriptions(),
            "totalMealsToDeliver", report.getTotalMealsToDeliver(),
            "totalIngredients", report.getIngredientSummary().size(),
            "topIngredients", report.getIngredientSummary().stream()
                .sorted((a, b) -> Integer.compare(b.getTotalWeightG(), a.getTotalWeightG()))
                .limit(5)
                .toList()
        );
        
        return ResponseEntity.ok(summary);
    }
}
