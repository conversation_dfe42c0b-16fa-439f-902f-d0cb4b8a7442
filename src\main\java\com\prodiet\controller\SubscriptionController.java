package com.prodiet.controller;

import com.prodiet.dto.CreateSubscriptionRequest;
import com.prodiet.dto.SubscriptionResponse;
import com.prodiet.service.SubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/subscriptions")
@Tag(name = "Subscription Management", description = "Subscription management APIs")
@SecurityRequirement(name = "Bearer Authentication")
public class SubscriptionController {

    @Autowired
    private SubscriptionService subscriptionService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get all subscriptions", description = "Retrieve all subscriptions (Admin only)")
    public ResponseEntity<List<SubscriptionResponse>> getAllSubscriptions() {
        List<SubscriptionResponse> subscriptions = subscriptionService.getAllSubscriptions();
        return ResponseEntity.ok(subscriptions);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get subscription by ID", description = "Retrieve a specific subscription by its ID (Admin only)")
    public ResponseEntity<SubscriptionResponse> getSubscriptionById(
            @Parameter(description = "Subscription ID") @PathVariable Long id) {
        SubscriptionResponse subscription = subscriptionService.getSubscriptionById(id);
        return ResponseEntity.ok(subscription);
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Create new subscription", description = "Create a new subscription for a customer (Admin only)")
    public ResponseEntity<SubscriptionResponse> createSubscription(@Valid @RequestBody CreateSubscriptionRequest request) {
        SubscriptionResponse createdSubscription = subscriptionService.createSubscription(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdSubscription);
    }

    @GetMapping("/customer/{customerId}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get subscriptions by customer", description = "Retrieve all subscriptions for a specific customer (Admin only)")
    public ResponseEntity<List<SubscriptionResponse>> getSubscriptionsByCustomerId(
            @Parameter(description = "Customer ID") @PathVariable Long customerId) {
        List<SubscriptionResponse> subscriptions = subscriptionService.getSubscriptionsByCustomerId(customerId);
        return ResponseEntity.ok(subscriptions);
    }

    @GetMapping("/customer/{customerId}/active")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('CUSTOMER') and #customerId == authentication.principal.id)")
    @Operation(summary = "Get active subscription by customer", description = "Retrieve the active subscription for a specific customer")
    public ResponseEntity<SubscriptionResponse> getActiveSubscriptionByCustomerId(
            @Parameter(description = "Customer ID") @PathVariable Long customerId) {
        SubscriptionResponse subscription = subscriptionService.getActiveSubscriptionByCustomerId(customerId);
        return ResponseEntity.ok(subscription);
    }

    @GetMapping("/active")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get all active subscriptions", description = "Retrieve all active subscriptions (Admin only)")
    public ResponseEntity<List<SubscriptionResponse>> getActiveSubscriptions() {
        List<SubscriptionResponse> subscriptions = subscriptionService.getActiveSubscriptions();
        return ResponseEntity.ok(subscriptions);
    }

    @GetMapping("/active/date/{date}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get active subscriptions for date", description = "Retrieve all active subscriptions for a specific date (Admin only)")
    public ResponseEntity<List<SubscriptionResponse>> getActiveSubscriptionsForDate(
            @Parameter(description = "Date (YYYY-MM-DD)") 
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        List<SubscriptionResponse> subscriptions = subscriptionService.getActiveSubscriptionsForDate(date);
        return ResponseEntity.ok(subscriptions);
    }

    @PutMapping("/{id}/cancel")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Cancel subscription", description = "Cancel a subscription (Admin only)")
    public ResponseEntity<Void> cancelSubscription(
            @Parameter(description = "Subscription ID") @PathVariable Long id) {
        subscriptionService.cancelSubscription(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/count/active")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get active subscription count", description = "Get the total number of active subscriptions (Admin only)")
    public ResponseEntity<Map<String, Long>> getActiveSubscriptionCount() {
        long count = subscriptionService.getActiveSubscriptionCount();
        return ResponseEntity.ok(Map.of("count", count));
    }
}
