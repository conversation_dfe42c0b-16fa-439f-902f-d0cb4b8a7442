package com.prodiet.controller;

import com.prodiet.dto.CustomerSubscriptionResponse;
import com.prodiet.dto.PausedDayResponse;
import com.prodiet.entity.User;
import com.prodiet.service.CustomerSubscriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1")
@Tag(name = "Customer Subscription Retrieval", description = "Customer subscription retrieval APIs")
@SecurityRequirement(name = "Bearer Authentication")
public class CustomerSubscriptionController {

    @Autowired
    private CustomerSubscriptionService customerSubscriptionService;

    @GetMapping("/customers/{id}/subscription")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('CUSTOMER') and #id == authentication.principal.id)")
    @Operation(summary = "Get customer subscription", 
               description = "Retrieve the active subscription details for a customer")
    public ResponseEntity<CustomerSubscriptionResponse> getCustomerSubscription(
            @Parameter(description = "Customer ID") @PathVariable Long id,
            Authentication authentication) {
        
        // Additional security check for customers
        if (authentication.getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals("ROLE_CUSTOMER"))) {
            User currentUser = (User) authentication.getPrincipal();
            if (!currentUser.getId().equals(id)) {
                return ResponseEntity.status(403).build();
            }
        }
        
        CustomerSubscriptionResponse subscription = customerSubscriptionService.getCustomerSubscription(id);
        return ResponseEntity.ok(subscription);
    }

    @GetMapping("/subscriptions/{id}/paused-days")
    @PreAuthorize("hasRole('ADMIN') or @customerSubscriptionService.isCustomerOwnerOfSubscription(authentication.principal.id, #id)")
    @Operation(summary = "Get subscription paused days", 
               description = "Retrieve all paused days for a subscription")
    public ResponseEntity<List<PausedDayResponse>> getSubscriptionPausedDays(
            @Parameter(description = "Subscription ID") @PathVariable Long id,
            Authentication authentication) {
        
        List<PausedDayResponse> pausedDays = customerSubscriptionService.getSubscriptionPausedDays(id);
        return ResponseEntity.ok(pausedDays);
    }

    @GetMapping("/customers/{id}/paused-days")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('CUSTOMER') and #id == authentication.principal.id)")
    @Operation(summary = "Get customer paused days", 
               description = "Retrieve all paused days for a customer's active subscription")
    public ResponseEntity<List<PausedDayResponse>> getCustomerPausedDays(
            @Parameter(description = "Customer ID") @PathVariable Long id,
            Authentication authentication) {
        
        // Additional security check for customers
        if (authentication.getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals("ROLE_CUSTOMER"))) {
            User currentUser = (User) authentication.getPrincipal();
            if (!currentUser.getId().equals(id)) {
                return ResponseEntity.status(403).build();
            }
        }
        
        List<PausedDayResponse> pausedDays = customerSubscriptionService.getCustomerPausedDays(id);
        return ResponseEntity.ok(pausedDays);
    }
}
