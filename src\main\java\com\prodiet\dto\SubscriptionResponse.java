package com.prodiet.dto;

import com.prodiet.entity.Subscription;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class SubscriptionResponse {

    private Long id;
    private Long customerId;
    private String customerName;
    private String customerEmail;
    private Long packageId;
    private String packageName;
    private LocalDate startDate;
    private LocalDate endDate;
    private Subscription.SubscriptionStatus status;
    private Subscription.FridayPreference fridayPreference;
    private Subscription.DeliveryMethod deliveryMethod;
    private String deliveryAddress;
    private String deliveryNotes;
    private Integer totalPausedDays;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public SubscriptionResponse() {}

    public SubscriptionResponse(Subscription subscription) {
        this.id = subscription.getId();
        this.customerId = subscription.getUser().getId();
        this.customerName = subscription.getUser().getName();
        this.customerEmail = subscription.getUser().getEmail();
        this.packageId = subscription.getSubscriptionPackage().getId();
        this.packageName = subscription.getSubscriptionPackage().getName();
        this.startDate = subscription.getStartDate();
        this.endDate = subscription.getEndDate();
        this.status = subscription.getStatus();
        this.fridayPreference = subscription.getFridayPreference();
        this.deliveryMethod = subscription.getDeliveryMethod();
        this.deliveryAddress = subscription.getDeliveryAddress();
        this.deliveryNotes = subscription.getDeliveryNotes();
        this.totalPausedDays = subscription.getTotalPausedDays();
        this.createdAt = subscription.getCreatedAt();
        this.updatedAt = subscription.getUpdatedAt();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerEmail() {
        return customerEmail;
    }

    public void setCustomerEmail(String customerEmail) {
        this.customerEmail = customerEmail;
    }

    public Long getPackageId() {
        return packageId;
    }

    public void setPackageId(Long packageId) {
        this.packageId = packageId;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Subscription.SubscriptionStatus getStatus() {
        return status;
    }

    public void setStatus(Subscription.SubscriptionStatus status) {
        this.status = status;
    }

    public Subscription.FridayPreference getFridayPreference() {
        return fridayPreference;
    }

    public void setFridayPreference(Subscription.FridayPreference fridayPreference) {
        this.fridayPreference = fridayPreference;
    }

    public Subscription.DeliveryMethod getDeliveryMethod() {
        return deliveryMethod;
    }

    public void setDeliveryMethod(Subscription.DeliveryMethod deliveryMethod) {
        this.deliveryMethod = deliveryMethod;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public String getDeliveryNotes() {
        return deliveryNotes;
    }

    public void setDeliveryNotes(String deliveryNotes) {
        this.deliveryNotes = deliveryNotes;
    }

    public Integer getTotalPausedDays() {
        return totalPausedDays;
    }

    public void setTotalPausedDays(Integer totalPausedDays) {
        this.totalPausedDays = totalPausedDays;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
