package com.prodiet.repository;

import com.prodiet.entity.Package;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PackageRepository extends JpaRepository<Package, Long> {

    List<Package> findByIsActiveTrue();

    List<Package> findByType(Package.PackageType type);

    List<Package> findByTypeAndIsActiveTrue(Package.PackageType type);

    Optional<Package> findByIdAndIsActiveTrue(Long id);

    @Query("SELECT p FROM Package p WHERE p.name LIKE %:name% AND p.isActive = true")
    List<Package> findByNameContainingAndIsActiveTrue(@Param("name") String name);

    @Query("SELECT COUNT(p) FROM Package p WHERE p.type = :type AND p.isActive = true")
    long countByTypeAndIsActiveTrue(@Param("type") Package.PackageType type);

    boolean existsByNameAndIsActiveTrue(String name);
}
