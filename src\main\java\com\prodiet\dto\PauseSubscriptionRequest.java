package com.prodiet.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDate;

public class PauseSubscriptionRequest {

    @NotNull(message = "Pause date is required")
    private LocalDate pauseDate;

    @Size(max = 200, message = "Reason cannot exceed 200 characters")
    private String reason;

    // Constructors
    public PauseSubscriptionRequest() {}

    public PauseSubscriptionRequest(LocalDate pauseDate, String reason) {
        this.pauseDate = pauseDate;
        this.reason = reason;
    }

    // Getters and Setters
    public LocalDate getPauseDate() {
        return pauseDate;
    }

    public void setPauseDate(LocalDate pauseDate) {
        this.pauseDate = pauseDate;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
