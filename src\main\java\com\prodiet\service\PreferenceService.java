package com.prodiet.service;

import com.prodiet.dto.PreferencesResponse;
import com.prodiet.dto.UpdatePreferencesRequest;
import com.prodiet.entity.*;
import com.prodiet.exception.ResourceNotFoundException;
import com.prodiet.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class PreferenceService {

    @Autowired
    private SubscriptionRepository subscriptionRepository;

    @Autowired
    private MealExclusionRepository mealExclusionRepository;

    @Autowired
    private DietaryPreferenceRepository dietaryPreferenceRepository;

    @Autowired
    private MealRepository mealRepository;

    @Autowired
    private UserRepository userRepository;

    public PreferencesResponse getCustomerPreferences(Long customerId) {
        // Verify customer exists and is active
        User customer = userRepository.findById(customerId)
                .orElseThrow(() -> new ResourceNotFoundException("Customer not found with id: " + customerId));

        if (customer.getRole() != User.Role.CUSTOMER || !customer.getIsActive()) {
            throw new ResourceNotFoundException("Customer not found with id: " + customerId);
        }

        // Get active subscription
        Subscription subscription = subscriptionRepository.findActiveSubscriptionByUserId(customerId)
                .orElseThrow(() -> new ResourceNotFoundException("No active subscription found for customer"));

        // Get current preferences
        List<MealExclusion> mealExclusions = mealExclusionRepository.findBySubscription(subscription);
        List<DietaryPreference> dietaryPreferences = dietaryPreferenceRepository.findBySubscription(subscription);

        return new PreferencesResponse(mealExclusions, dietaryPreferences);
    }

    public PreferencesResponse updateCustomerPreferences(Long customerId, UpdatePreferencesRequest request) {
        // Verify customer exists and is active
        User customer = userRepository.findById(customerId)
                .orElseThrow(() -> new ResourceNotFoundException("Customer not found with id: " + customerId));

        if (customer.getRole() != User.Role.CUSTOMER || !customer.getIsActive()) {
            throw new ResourceNotFoundException("Customer not found with id: " + customerId);
        }

        // Get active subscription
        Subscription subscription = subscriptionRepository.findActiveSubscriptionByUserId(customerId)
                .orElseThrow(() -> new ResourceNotFoundException("No active subscription found for customer"));

        // Validate meal IDs exist and are active
        if (request.getExcludedMealIds() != null && !request.getExcludedMealIds().isEmpty()) {
            for (Long mealId : request.getExcludedMealIds()) {
                if (!mealRepository.findByIdAndIsActiveTrue(mealId).isPresent()) {
                    throw new ResourceNotFoundException("Meal not found with id: " + mealId);
                }
            }
        }

        // Update meal exclusions
        updateMealExclusions(subscription, request.getExcludedMealIds());

        // Update dietary preferences
        updateDietaryPreferences(subscription, request.getDietaryPreferences());

        // Return updated preferences
        return getCustomerPreferences(customerId);
    }

    private void updateMealExclusions(Subscription subscription, List<Long> excludedMealIds) {
        // Remove all existing meal exclusions
        mealExclusionRepository.deleteBySubscriptionId(subscription.getId());

        // Add new meal exclusions
        if (excludedMealIds != null && !excludedMealIds.isEmpty()) {
            for (Long mealId : excludedMealIds) {
                Meal meal = mealRepository.findByIdAndIsActiveTrue(mealId)
                        .orElseThrow(() -> new ResourceNotFoundException("Meal not found with id: " + mealId));

                MealExclusion mealExclusion = new MealExclusion(subscription, meal);
                mealExclusionRepository.save(mealExclusion);
            }
        }
    }

    private void updateDietaryPreferences(Subscription subscription,
            List<UpdatePreferencesRequest.DietaryPreferenceDto> preferenceDtos) {
        // Remove all existing dietary preferences
        dietaryPreferenceRepository.deleteBySubscriptionId(subscription.getId());

        // Add new dietary preferences
        if (preferenceDtos != null && !preferenceDtos.isEmpty()) {
            for (UpdatePreferencesRequest.DietaryPreferenceDto dto : preferenceDtos) {
                if (dto.getPreferenceText() != null && !dto.getPreferenceText().trim().isEmpty()) {
                    DietaryPreference preference = new DietaryPreference(
                            dto.getPreferenceText().trim(),
                            dto.getPreferenceType() != null ? dto.getPreferenceType()
                                    : DietaryPreference.PreferenceType.OTHER,
                            subscription);
                    dietaryPreferenceRepository.save(preference);
                }
            }
        }
    }

    public boolean hasCustomerExcludedMeal(Long customerId, Long mealId) {
        Subscription subscription = subscriptionRepository.findActiveSubscriptionByUserId(customerId)
                .orElse(null);

        if (subscription == null) {
            return false;
        }

        return mealExclusionRepository.existsBySubscriptionIdAndMealId(subscription.getId(), mealId);
    }

    public List<Long> getExcludedMealIds(Long subscriptionId) {
        return mealExclusionRepository.findBySubscriptionId(subscriptionId)
                .stream()
                .map(exclusion -> exclusion.getMeal().getId())
                .collect(Collectors.toList());
    }
}
