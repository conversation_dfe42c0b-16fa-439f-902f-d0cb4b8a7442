package com.prodiet.repository;

import com.prodiet.entity.PausedDay;
import com.prodiet.entity.Subscription;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface PausedDayRepository extends JpaRepository<PausedDay, Long> {

    List<PausedDay> findBySubscription(Subscription subscription);

    List<PausedDay> findBySubscriptionId(Long subscriptionId);

    List<PausedDay> findBySubscriptionIdOrderByPausedDateAsc(Long subscriptionId);

    @Query("SELECT pd FROM PausedDay pd WHERE pd.subscription.id = :subscriptionId AND pd.pausedDate >= :fromDate")
    List<PausedDay> findBySubscriptionIdAndPausedDateAfter(@Param("subscriptionId") Long subscriptionId, 
                                                           @Param("fromDate") LocalDate fromDate);

    @Query("SELECT pd FROM PausedDay pd WHERE pd.subscription.id = :subscriptionId AND pd.pausedDate BETWEEN :startDate AND :endDate")
    List<PausedDay> findBySubscriptionIdAndPausedDateBetween(@Param("subscriptionId") Long subscriptionId,
                                                             @Param("startDate") LocalDate startDate,
                                                             @Param("endDate") LocalDate endDate);

    @Query("SELECT COUNT(pd) FROM PausedDay pd WHERE pd.subscription.id = :subscriptionId")
    long countBySubscriptionId(@Param("subscriptionId") Long subscriptionId);

    @Query("SELECT COUNT(pd) FROM PausedDay pd WHERE pd.subscription.id = :subscriptionId AND pd.pausedDate >= :fromDate")
    long countBySubscriptionIdAndPausedDateAfter(@Param("subscriptionId") Long subscriptionId, 
                                                 @Param("fromDate") LocalDate fromDate);

    boolean existsBySubscriptionIdAndPausedDate(Long subscriptionId, LocalDate pausedDate);

    void deleteBySubscriptionIdAndPausedDate(Long subscriptionId, LocalDate pausedDate);
}
