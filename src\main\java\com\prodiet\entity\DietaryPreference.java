package com.prodiet.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

@Entity
@Table(name = "dietary_preferences")
public class DietaryPreference {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Preference text is required")
    @Size(min = 2, max = 200, message = "Preference text must be between 2 and 200 characters")
    @Column(name = "preference_text", nullable = false, length = 200)
    private String preferenceText;

    @Enumerated(EnumType.STRING)
    @Column(name = "preference_type")
    private PreferenceType preferenceType;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subscription_id", nullable = false)
    private Subscription subscription;

    public enum PreferenceType {
        ALLERGY, DISLIKE, DIETARY_RESTRICTION, OTHER
    }

    // Constructors
    public DietaryPreference() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public DietaryPreference(String preferenceText, PreferenceType preferenceType, Subscription subscription) {
        this();
        this.preferenceText = preferenceText;
        this.preferenceType = preferenceType;
        this.subscription = subscription;
    }

    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPreferenceText() {
        return preferenceText;
    }

    public void setPreferenceText(String preferenceText) {
        this.preferenceText = preferenceText;
    }

    public PreferenceType getPreferenceType() {
        return preferenceType;
    }

    public void setPreferenceType(PreferenceType preferenceType) {
        this.preferenceType = preferenceType;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Subscription getSubscription() {
        return subscription;
    }

    public void setSubscription(Subscription subscription) {
        this.subscription = subscription;
    }
}
