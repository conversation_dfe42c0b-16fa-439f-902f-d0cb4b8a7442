package com.prodiet.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "subscriptions")
public class Subscription {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "Start date is required")
    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;

    @NotNull(message = "End date is required")
    @Column(name = "end_date", nullable = false)
    private LocalDate endDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private SubscriptionStatus status = SubscriptionStatus.ACTIVE;

    @Enumerated(EnumType.STRING)
    @Column(name = "friday_preference", nullable = false)
    private FridayPreference fridayPreference = FridayPreference.SKIP_AND_EXTEND;

    @Enumerated(EnumType.STRING)
    @Column(name = "delivery_method", nullable = false)
    private DeliveryMethod deliveryMethod = DeliveryMethod.PICKUP;

    @Column(name = "delivery_address", length = 500)
    private String deliveryAddress;

    @Column(name = "delivery_notes", length = 200)
    private String deliveryNotes;

    @Column(name = "total_paused_days", nullable = false)
    private Integer totalPausedDays = 0;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "package_id", nullable = false)
    private Package subscriptionPackage;

    @OneToMany(mappedBy = "subscription", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<MealExclusion> mealExclusions = new ArrayList<>();

    @OneToMany(mappedBy = "subscription", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<DietaryPreference> dietaryPreferences = new ArrayList<>();

    @OneToMany(mappedBy = "subscription", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<PausedDay> pausedDays = new ArrayList<>();

    public enum SubscriptionStatus {
        ACTIVE, PAUSED, COMPLETED, CANCELLED
    }

    public enum FridayPreference {
        RECEIVE_ON_THURSDAY, SKIP_AND_EXTEND
    }

    public enum DeliveryMethod {
        DELIVERY, PICKUP
    }

    // Constructors
    public Subscription() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public Subscription(User user, Package subscriptionPackage, LocalDate startDate, LocalDate endDate) {
        this();
        this.user = user;
        this.subscriptionPackage = subscriptionPackage;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // Helper methods
    public void addMealExclusion(MealExclusion mealExclusion) {
        mealExclusions.add(mealExclusion);
        mealExclusion.setSubscription(this);
    }

    public void removeMealExclusion(MealExclusion mealExclusion) {
        mealExclusions.remove(mealExclusion);
        mealExclusion.setSubscription(null);
    }

    public void addDietaryPreference(DietaryPreference dietaryPreference) {
        dietaryPreferences.add(dietaryPreference);
        dietaryPreference.setSubscription(this);
    }

    public void removeDietaryPreference(DietaryPreference dietaryPreference) {
        dietaryPreferences.remove(dietaryPreference);
        dietaryPreference.setSubscription(null);
    }

    public void addPausedDay(PausedDay pausedDay) {
        pausedDays.add(pausedDay);
        pausedDay.setSubscription(this);
        this.totalPausedDays = pausedDays.size();
    }

    public void removePausedDay(PausedDay pausedDay) {
        pausedDays.remove(pausedDay);
        pausedDay.setSubscription(null);
        this.totalPausedDays = pausedDays.size();
    }

    public boolean isActive() {
        return status == SubscriptionStatus.ACTIVE && 
               LocalDate.now().isAfter(startDate.minusDays(1)) && 
               LocalDate.now().isBefore(endDate.plusDays(1));
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public SubscriptionStatus getStatus() {
        return status;
    }

    public void setStatus(SubscriptionStatus status) {
        this.status = status;
    }

    public FridayPreference getFridayPreference() {
        return fridayPreference;
    }

    public void setFridayPreference(FridayPreference fridayPreference) {
        this.fridayPreference = fridayPreference;
    }

    public DeliveryMethod getDeliveryMethod() {
        return deliveryMethod;
    }

    public void setDeliveryMethod(DeliveryMethod deliveryMethod) {
        this.deliveryMethod = deliveryMethod;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public String getDeliveryNotes() {
        return deliveryNotes;
    }

    public void setDeliveryNotes(String deliveryNotes) {
        this.deliveryNotes = deliveryNotes;
    }

    public Integer getTotalPausedDays() {
        return totalPausedDays;
    }

    public void setTotalPausedDays(Integer totalPausedDays) {
        this.totalPausedDays = totalPausedDays;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Package getSubscriptionPackage() {
        return subscriptionPackage;
    }

    public void setSubscriptionPackage(Package subscriptionPackage) {
        this.subscriptionPackage = subscriptionPackage;
    }

    public List<MealExclusion> getMealExclusions() {
        return mealExclusions;
    }

    public void setMealExclusions(List<MealExclusion> mealExclusions) {
        this.mealExclusions = mealExclusions;
    }

    public List<DietaryPreference> getDietaryPreferences() {
        return dietaryPreferences;
    }

    public void setDietaryPreferences(List<DietaryPreference> dietaryPreferences) {
        this.dietaryPreferences = dietaryPreferences;
    }

    public List<PausedDay> getPausedDays() {
        return pausedDays;
    }

    public void setPausedDays(List<PausedDay> pausedDays) {
        this.pausedDays = pausedDays;
    }
}
