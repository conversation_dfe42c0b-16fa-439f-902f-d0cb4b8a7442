package com.prodiet.dto;

import com.prodiet.entity.Package;

import java.time.LocalDateTime;

public class PackageResponse {

    private Long id;
    private String name;
    private String description;
    private Package.PackageType type;
    private Integer durationDays;
    private Integer dailyProteinG;
    private Integer dailyCarbsG;
    private Integer mealsPerDay;
    private Integer mealsPerWeek;
    private Integer frequencyPerWeek;
    private String productionDays;
    private Double price;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public PackageResponse() {}

    public PackageResponse(Package pkg) {
        this.id = pkg.getId();
        this.name = pkg.getName();
        this.description = pkg.getDescription();
        this.type = pkg.getType();
        this.durationDays = pkg.getDurationDays();
        this.dailyProteinG = pkg.getDailyProteinG();
        this.dailyCarbsG = pkg.getDailyCarbsG();
        this.mealsPerDay = pkg.getMealsPerDay();
        this.mealsPerWeek = pkg.getMealsPerWeek();
        this.frequencyPerWeek = pkg.getFrequencyPerWeek();
        this.productionDays = pkg.getProductionDays();
        this.price = pkg.getPrice();
        this.isActive = pkg.getIsActive();
        this.createdAt = pkg.getCreatedAt();
        this.updatedAt = pkg.getUpdatedAt();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Package.PackageType getType() {
        return type;
    }

    public void setType(Package.PackageType type) {
        this.type = type;
    }

    public Integer getDurationDays() {
        return durationDays;
    }

    public void setDurationDays(Integer durationDays) {
        this.durationDays = durationDays;
    }

    public Integer getDailyProteinG() {
        return dailyProteinG;
    }

    public void setDailyProteinG(Integer dailyProteinG) {
        this.dailyProteinG = dailyProteinG;
    }

    public Integer getDailyCarbsG() {
        return dailyCarbsG;
    }

    public void setDailyCarbsG(Integer dailyCarbsG) {
        this.dailyCarbsG = dailyCarbsG;
    }

    public Integer getMealsPerDay() {
        return mealsPerDay;
    }

    public void setMealsPerDay(Integer mealsPerDay) {
        this.mealsPerDay = mealsPerDay;
    }

    public Integer getMealsPerWeek() {
        return mealsPerWeek;
    }

    public void setMealsPerWeek(Integer mealsPerWeek) {
        this.mealsPerWeek = mealsPerWeek;
    }

    public Integer getFrequencyPerWeek() {
        return frequencyPerWeek;
    }

    public void setFrequencyPerWeek(Integer frequencyPerWeek) {
        this.frequencyPerWeek = frequencyPerWeek;
    }

    public String getProductionDays() {
        return productionDays;
    }

    public void setProductionDays(String productionDays) {
        this.productionDays = productionDays;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
