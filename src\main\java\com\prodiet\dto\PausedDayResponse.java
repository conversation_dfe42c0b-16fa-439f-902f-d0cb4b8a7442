package com.prodiet.dto;

import com.prodiet.entity.PausedDay;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class PausedDayResponse {

    private Long id;
    private LocalDate pausedDate;
    private String reason;
    private LocalDateTime createdAt;

    // Constructors
    public PausedDayResponse() {}

    public PausedDayResponse(PausedDay pausedDay) {
        this.id = pausedDay.getId();
        this.pausedDate = pausedDay.getPausedDate();
        this.reason = pausedDay.getReason();
        this.createdAt = pausedDay.getCreatedAt();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getPausedDate() {
        return pausedDate;
    }

    public void setPausedDate(LocalDate pausedDate) {
        this.pausedDate = pausedDate;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}
