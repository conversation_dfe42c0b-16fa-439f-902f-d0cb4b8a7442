package com.prodiet.service;

import com.prodiet.dto.ProductionReportDto;
import com.prodiet.entity.*;
import com.prodiet.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = true)
public class ProductionService {

    @Autowired
    private SubscriptionRepository subscriptionRepository;

    @Autowired
    private MealRepository mealRepository;

    @Autowired
    private MealComponentRepository mealComponentRepository;

    @Autowired
    private MealExclusionRepository mealExclusionRepository;

    @Autowired
    private FridayService fridayService;

    /**
     * Generates a complete production report for a given date
     */
    public ProductionReportDto generateProductionReport(LocalDate date) {
        ProductionReportDto report = new ProductionReportDto(date);

        // Get active subscriptions for the date
        List<Subscription> activeSubscriptions = getActiveSubscriptionsForDate(date);
        report.setTotalActiveSubscriptions(activeSubscriptions.size());

        // Calculate customer breakdown
        List<ProductionReportDto.CustomerMealDto> customerBreakdown = generateCustomerBreakdown(activeSubscriptions, date);
        report.setCustomerBreakdown(customerBreakdown);

        // Calculate total meals
        int totalMeals = customerBreakdown.stream()
                .mapToInt(customer -> customer.getMeals().size() * customer.getMealMultiplier())
                .sum();
        report.setTotalMealsToDeliver(totalMeals);

        // Calculate ingredient summary
        List<ProductionReportDto.IngredientSummaryDto> ingredientSummary = generateIngredientSummary(customerBreakdown);
        report.setIngredientSummary(ingredientSummary);

        return report;
    }

    /**
     * Gets active subscriptions for a specific date, considering Friday preferences and paused days
     */
    public List<Subscription> getActiveSubscriptionsForDate(LocalDate date) {
        // Get all potentially active subscriptions for the date
        List<Subscription> subscriptions = subscriptionRepository.findActiveSubscriptionsForDate(date);

        // Filter based on Friday preferences and paused days
        return subscriptions.stream()
                .filter(subscription -> fridayService.shouldReceiveMealsOnDate(subscription, date) ||
                                      fridayService.shouldReceiveFridayMealsOnThursday(subscription, date))
                .collect(Collectors.toList());
    }

    /**
     * Generates customer breakdown with meal details
     */
    public List<ProductionReportDto.CustomerMealDto> generateCustomerBreakdown(List<Subscription> subscriptions, LocalDate date) {
        List<ProductionReportDto.CustomerMealDto> customerBreakdown = new ArrayList<>();

        for (Subscription subscription : subscriptions) {
            User customer = subscription.getUser();
            int mealMultiplier = fridayService.getMealMultiplier(subscription, date);

            if (mealMultiplier == 0) {
                continue; // Skip if no meals for this customer on this date
            }

            ProductionReportDto.CustomerMealDto customerDto = new ProductionReportDto.CustomerMealDto(
                customer.getId(),
                customer.getName(),
                customer.getEmail(),
                mealMultiplier,
                subscription.getFridayPreference().name()
            );

            // Get meals for the package, excluding customer's excluded meals
            List<Meal> packageMeals = getPackageMealsForCustomer(subscription);
            List<ProductionReportDto.MealDetailDto> mealDetails = new ArrayList<>();

            for (Meal meal : packageMeals) {
                ProductionReportDto.MealDetailDto mealDto = new ProductionReportDto.MealDetailDto(
                    meal.getId(),
                    meal.getName(),
                    meal.getMealType().name()
                );

                // Get meal components
                List<MealComponent> components = mealComponentRepository.findByMealId(meal.getId());
                List<ProductionReportDto.ComponentDetailDto> componentDetails = components.stream()
                        .map(component -> new ProductionReportDto.ComponentDetailDto(
                            component.getIngredientName(),
                            component.getWeightG(),
                            component.getWeightG() * mealMultiplier,
                            component.getPreparationNotes()
                        ))
                        .collect(Collectors.toList());

                mealDto.setComponents(componentDetails);
                mealDetails.add(mealDto);
            }

            customerDto.setMeals(mealDetails);
            customerBreakdown.add(customerDto);
        }

        return customerBreakdown;
    }

    /**
     * Generates aggregated ingredient summary
     */
    public List<ProductionReportDto.IngredientSummaryDto> generateIngredientSummary(List<ProductionReportDto.CustomerMealDto> customerBreakdown) {
        Map<String, Integer> ingredientTotals = new HashMap<>();

        for (ProductionReportDto.CustomerMealDto customer : customerBreakdown) {
            for (ProductionReportDto.MealDetailDto meal : customer.getMeals()) {
                for (ProductionReportDto.ComponentDetailDto component : meal.getComponents()) {
                    ingredientTotals.merge(component.getIngredientName(), component.getTotalWeightG(), Integer::sum);
                }
            }
        }

        return ingredientTotals.entrySet().stream()
                .map(entry -> new ProductionReportDto.IngredientSummaryDto(entry.getKey(), entry.getValue()))
                .sorted(Comparator.comparing(ProductionReportDto.IngredientSummaryDto::getIngredientName))
                .collect(Collectors.toList());
    }

    /**
     * Gets meals for a package, excluding customer's excluded meals
     */
    private List<Meal> getPackageMealsForCustomer(Subscription subscription) {
        // Get all meals for the package
        List<Meal> packageMeals = mealRepository.findByPackageIdAndIsActiveTrue(subscription.getSubscriptionPackage().getId());

        // Get customer's excluded meal IDs
        Set<Long> excludedMealIds = mealExclusionRepository.findBySubscriptionId(subscription.getId())
                .stream()
                .map(exclusion -> exclusion.getMeal().getId())
                .collect(Collectors.toSet());

        // Filter out excluded meals
        return packageMeals.stream()
                .filter(meal -> !excludedMealIds.contains(meal.getId()))
                .collect(Collectors.toList());
    }

    /**
     * Gets production statistics for a date range
     */
    public Map<String, Object> getProductionStats(LocalDate startDate, LocalDate endDate) {
        Map<String, Object> stats = new HashMap<>();
        
        List<LocalDate> dates = startDate.datesUntil(endDate.plusDays(1)).collect(Collectors.toList());
        
        int totalDays = dates.size();
        int totalSubscriptions = 0;
        int totalMeals = 0;
        
        for (LocalDate date : dates) {
            List<Subscription> activeSubscriptions = getActiveSubscriptionsForDate(date);
            totalSubscriptions += activeSubscriptions.size();
            
            for (Subscription subscription : activeSubscriptions) {
                totalMeals += fridayService.getEffectiveMealCount(subscription, date);
            }
        }
        
        stats.put("dateRange", Map.of("start", startDate, "end", endDate));
        stats.put("totalDays", totalDays);
        stats.put("totalSubscriptions", totalSubscriptions);
        stats.put("totalMeals", totalMeals);
        stats.put("averageSubscriptionsPerDay", totalDays > 0 ? (double) totalSubscriptions / totalDays : 0);
        stats.put("averageMealsPerDay", totalDays > 0 ? (double) totalMeals / totalDays : 0);
        
        return stats;
    }

    /**
     * Gets the most used ingredients across all active subscriptions
     */
    public List<ProductionReportDto.IngredientSummaryDto> getMostUsedIngredients(int limit) {
        LocalDate today = LocalDate.now();
        List<Subscription> activeSubscriptions = getActiveSubscriptionsForDate(today);
        List<ProductionReportDto.CustomerMealDto> customerBreakdown = generateCustomerBreakdown(activeSubscriptions, today);
        
        return generateIngredientSummary(customerBreakdown).stream()
                .sorted(Comparator.comparing(ProductionReportDto.IngredientSummaryDto::getTotalWeightG).reversed())
                .limit(limit)
                .collect(Collectors.toList());
    }
}
