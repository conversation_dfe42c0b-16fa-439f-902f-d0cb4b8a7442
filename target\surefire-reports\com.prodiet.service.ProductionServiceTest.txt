-------------------------------------------------------------------------------
Test set: com.prodiet.service.ProductionServiceTest
-------------------------------------------------------------------------------
Tests run: 6, Failures: 0, Errors: 6, Skipped: 0, Time elapsed: 0.756 s <<< FAILURE! -- in com.prodiet.service.ProductionServiceTest
com.prodiet.service.ProductionServiceTest.testGenerateProductionReport_ThursdayWithFridayPreference -- Time elapsed: 0.686 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.prodiet.service.ProductionServiceTest.testGenerateProductionReport_ThursdayWithFridayPreference(ProductionServiceTest.java:152)
  2. -> at com.prodiet.service.ProductionServiceTest.testGenerateProductionReport_ThursdayWithFridayPreference(ProductionServiceTest.java:153)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:192)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.prodiet.service.ProductionServiceTest.testGetProductionStats -- Time elapsed: 0.022 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.prodiet.service.ProductionServiceTest.testGetProductionStats(ProductionServiceTest.java:251)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:192)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.prodiet.service.ProductionServiceTest.testGetMostUsedIngredients -- Time elapsed: 0.009 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.prodiet.service.ProductionServiceTest.testGetMostUsedIngredients(ProductionServiceTest.java:275)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:192)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.prodiet.service.ProductionServiceTest.testGetActiveSubscriptionsForDate -- Time elapsed: 0.006 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.prodiet.service.ProductionServiceTest.testGetActiveSubscriptionsForDate(ProductionServiceTest.java:233)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:192)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.prodiet.service.ProductionServiceTest.testGenerateProductionReport_RegularDay -- Time elapsed: 0.010 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.prodiet.service.ProductionServiceTest.testGenerateProductionReport_RegularDay(ProductionServiceTest.java:106)
  2. -> at com.prodiet.service.ProductionServiceTest.testGenerateProductionReport_RegularDay(ProductionServiceTest.java:107)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:192)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.prodiet.service.ProductionServiceTest.testGenerateProductionReport_WithMealExclusions -- Time elapsed: 0.008 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.prodiet.service.ProductionServiceTest.testGenerateProductionReport_WithMealExclusions(ProductionServiceTest.java:204)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:192)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

