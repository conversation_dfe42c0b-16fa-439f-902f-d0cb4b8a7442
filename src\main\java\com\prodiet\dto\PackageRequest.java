package com.prodiet.dto;

import com.prodiet.entity.Package;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

public class PackageRequest {

    @NotBlank(message = "Package name is required")
    @Size(min = 2, max = 100, message = "Package name must be between 2 and 100 characters")
    private String name;

    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    @NotNull(message = "Package type is required")
    private Package.PackageType type;

    // Standard Package fields
    @Min(value = 1, message = "Duration must be at least 1 day")
    private Integer durationDays;

    @Min(value = 0, message = "Protein allowance cannot be negative")
    private Integer dailyProteinG;

    @Min(value = 0, message = "Carbohydrate allowance cannot be negative")
    private Integer dailyCarbsG;

    @Min(value = 1, message = "Meals per day must be at least 1")
    private Integer mealsPerDay;

    // Add-on Package fields
    @Min(value = 1, message = "Meals per week must be at least 1")
    private Integer mealsPerWeek;

    @Min(value = 1, message = "Frequency must be at least 1")
    private Integer frequencyPerWeek;

    private String productionDays;

    @NotNull(message = "Price is required")
    @Min(value = 0, message = "Price cannot be negative")
    private Double price;

    // Constructors
    public PackageRequest() {}

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Package.PackageType getType() {
        return type;
    }

    public void setType(Package.PackageType type) {
        this.type = type;
    }

    public Integer getDurationDays() {
        return durationDays;
    }

    public void setDurationDays(Integer durationDays) {
        this.durationDays = durationDays;
    }

    public Integer getDailyProteinG() {
        return dailyProteinG;
    }

    public void setDailyProteinG(Integer dailyProteinG) {
        this.dailyProteinG = dailyProteinG;
    }

    public Integer getDailyCarbsG() {
        return dailyCarbsG;
    }

    public void setDailyCarbsG(Integer dailyCarbsG) {
        this.dailyCarbsG = dailyCarbsG;
    }

    public Integer getMealsPerDay() {
        return mealsPerDay;
    }

    public void setMealsPerDay(Integer mealsPerDay) {
        this.mealsPerDay = mealsPerDay;
    }

    public Integer getMealsPerWeek() {
        return mealsPerWeek;
    }

    public void setMealsPerWeek(Integer mealsPerWeek) {
        this.mealsPerWeek = mealsPerWeek;
    }

    public Integer getFrequencyPerWeek() {
        return frequencyPerWeek;
    }

    public void setFrequencyPerWeek(Integer frequencyPerWeek) {
        this.frequencyPerWeek = frequencyPerWeek;
    }

    public String getProductionDays() {
        return productionDays;
    }

    public void setProductionDays(String productionDays) {
        this.productionDays = productionDays;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }
}
