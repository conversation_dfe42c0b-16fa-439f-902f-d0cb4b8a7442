package com.prodiet.dto;

import com.prodiet.entity.Subscription;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDate;

public class CreateSubscriptionRequest {

    @NotNull(message = "Customer ID is required")
    private Long customerId;

    @NotNull(message = "Package ID is required")
    private Long packageId;

    @NotNull(message = "Start date is required")
    private LocalDate startDate;

    private Subscription.FridayPreference fridayPreference = Subscription.FridayPreference.SKIP_AND_EXTEND;

    private Subscription.DeliveryMethod deliveryMethod = Subscription.DeliveryMethod.PICKUP;

    @Size(max = 500, message = "Delivery address cannot exceed 500 characters")
    private String deliveryAddress;

    @Size(max = 200, message = "Delivery notes cannot exceed 200 characters")
    private String deliveryNotes;

    // Constructors
    public CreateSubscriptionRequest() {}

    public CreateSubscriptionRequest(Long customerId, Long packageId, LocalDate startDate) {
        this.customerId = customerId;
        this.packageId = packageId;
        this.startDate = startDate;
    }

    // Getters and Setters
    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getPackageId() {
        return packageId;
    }

    public void setPackageId(Long packageId) {
        this.packageId = packageId;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public Subscription.FridayPreference getFridayPreference() {
        return fridayPreference;
    }

    public void setFridayPreference(Subscription.FridayPreference fridayPreference) {
        this.fridayPreference = fridayPreference;
    }

    public Subscription.DeliveryMethod getDeliveryMethod() {
        return deliveryMethod;
    }

    public void setDeliveryMethod(Subscription.DeliveryMethod deliveryMethod) {
        this.deliveryMethod = deliveryMethod;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public String getDeliveryNotes() {
        return deliveryNotes;
    }

    public void setDeliveryNotes(String deliveryNotes) {
        this.deliveryNotes = deliveryNotes;
    }
}
