package com.prodiet.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.prodiet.dto.CreateCustomerRequest;
import com.prodiet.dto.LoginRequest;
import com.prodiet.dto.LoginResponse;
import com.prodiet.entity.User;
import com.prodiet.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class AuthenticationIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private User adminUser;
    private User customerUser;

    @BeforeEach
    void setUp() {
        // Create admin user
        adminUser = new User("Admin User", "<EMAIL>", 
                           passwordEncoder.encode("admin123"), User.Role.ADMIN);
        adminUser.setIsActive(true);
        adminUser = userRepository.save(adminUser);

        // Create customer user
        customerUser = new User("Customer User", "<EMAIL>", 
                              passwordEncoder.encode("customer123"), User.Role.CUSTOMER);
        customerUser.setIsActive(true);
        customerUser = userRepository.save(customerUser);
    }

    @Test
    void testAdminLogin_Success() throws Exception {
        LoginRequest loginRequest = new LoginRequest("<EMAIL>", "admin123");

        MvcResult result = mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.token").exists())
                .andExpect(jsonPath("$.userId").value(adminUser.getId()))
                .andExpect(jsonPath("$.name").value("Admin User"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.role").value("ADMIN"))
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        LoginResponse loginResponse = objectMapper.readValue(responseContent, LoginResponse.class);
        
        assertNotNull(loginResponse.getToken());
        assertTrue(loginResponse.getToken().length() > 0);
    }

    @Test
    void testCustomerLogin_Success() throws Exception {
        LoginRequest loginRequest = new LoginRequest("<EMAIL>", "customer123");

        mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.token").exists())
                .andExpect(jsonPath("$.userId").value(customerUser.getId()))
                .andExpect(jsonPath("$.name").value("Customer User"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.role").value("CUSTOMER"));
    }

    @Test
    void testLogin_InvalidCredentials() throws Exception {
        LoginRequest loginRequest = new LoginRequest("<EMAIL>", "wrongpassword");

        mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testLogin_UserNotFound() throws Exception {
        LoginRequest loginRequest = new LoginRequest("<EMAIL>", "password");

        mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testAccessProtectedEndpoint_WithValidToken() throws Exception {
        // First, login to get token
        LoginRequest loginRequest = new LoginRequest("<EMAIL>", "admin123");
        
        MvcResult loginResult = mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andReturn();

        String loginResponseContent = loginResult.getResponse().getContentAsString();
        LoginResponse loginResponse = objectMapper.readValue(loginResponseContent, LoginResponse.class);
        String token = loginResponse.getToken();

        // Then, access protected endpoint with token
        mockMvc.perform(get("/api/v1/packages")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk());
    }

    @Test
    void testAccessProtectedEndpoint_WithoutToken() throws Exception {
        mockMvc.perform(get("/api/v1/packages"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void testAccessProtectedEndpoint_WithInvalidToken() throws Exception {
        mockMvc.perform(get("/api/v1/packages")
                .header("Authorization", "Bearer invalid-token"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void testAccessAdminEndpoint_AsCustomer() throws Exception {
        // Login as customer
        LoginRequest loginRequest = new LoginRequest("<EMAIL>", "customer123");
        
        MvcResult loginResult = mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andReturn();

        String loginResponseContent = loginResult.getResponse().getContentAsString();
        LoginResponse loginResponse = objectMapper.readValue(loginResponseContent, LoginResponse.class);
        String token = loginResponse.getToken();

        // Try to access admin endpoint
        mockMvc.perform(get("/api/v1/packages")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isForbidden());
    }

    @Test
    void testCreateCustomer_AsAdmin() throws Exception {
        // Login as admin
        LoginRequest loginRequest = new LoginRequest("<EMAIL>", "admin123");
        
        MvcResult loginResult = mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andReturn();

        String loginResponseContent = loginResult.getResponse().getContentAsString();
        LoginResponse loginResponse = objectMapper.readValue(loginResponseContent, LoginResponse.class);
        String token = loginResponse.getToken();

        // Create customer
        CreateCustomerRequest createRequest = new CreateCustomerRequest(
                "New Customer", "<EMAIL>", "password123");

        mockMvc.perform(post("/api/v1/customers")
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("New Customer"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.role").value("CUSTOMER"));
    }

    @Test
    void testValidateToken_ValidToken() throws Exception {
        // Login to get token
        LoginRequest loginRequest = new LoginRequest("<EMAIL>", "admin123");
        
        MvcResult loginResult = mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andReturn();

        String loginResponseContent = loginResult.getResponse().getContentAsString();
        LoginResponse loginResponse = objectMapper.readValue(loginResponseContent, LoginResponse.class);
        String token = loginResponse.getToken();

        // Validate token
        mockMvc.perform(post("/api/v1/auth/validate")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.valid").value(true));
    }

    @Test
    void testValidateToken_InvalidToken() throws Exception {
        mockMvc.perform(post("/api/v1/auth/validate")
                .header("Authorization", "Bearer invalid-token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.valid").value(false));
    }
}
