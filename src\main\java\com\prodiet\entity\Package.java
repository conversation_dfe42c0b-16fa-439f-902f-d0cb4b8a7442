package com.prodiet.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "packages")
public class Package {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Package name is required")
    @Size(min = 2, max = 100, message = "Package name must be between 2 and 100 characters")
    @Column(nullable = false, length = 100)
    private String name;

    @Column(length = 500)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PackageType type;

    // Standard Package fields
    @Min(value = 1, message = "Duration must be at least 1 day")
    @Column(name = "duration_days")
    private Integer durationDays;

    @Min(value = 0, message = "Protein allowance cannot be negative")
    @Column(name = "daily_protein_g")
    private Integer dailyProteinG;

    @Min(value = 0, message = "Carbohydrate allowance cannot be negative")
    @Column(name = "daily_carbs_g")
    private Integer dailyCarbsG;

    @Min(value = 1, message = "Meals per day must be at least 1")
    @Column(name = "meals_per_day")
    private Integer mealsPerDay;

    // Add-on Package fields
    @Min(value = 1, message = "Meals per week must be at least 1")
    @Column(name = "meals_per_week")
    private Integer mealsPerWeek;

    @Min(value = 1, message = "Frequency must be at least 1")
    @Column(name = "frequency_per_week")
    private Integer frequencyPerWeek;

    @Column(name = "production_days", length = 100)
    private String productionDays; // Comma-separated days (e.g., "MONDAY,WEDNESDAY,FRIDAY")

    @NotNull(message = "Price is required")
    @Min(value = 0, message = "Price cannot be negative")
    @Column(nullable = false)
    private Double price;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Relationships
    @ManyToMany(mappedBy = "packages", fetch = FetchType.LAZY)
    private List<Meal> meals = new ArrayList<>();

    public enum PackageType {
        STANDARD, ADD_ON
    }

    // Constructors
    public Package() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public Package(String name, PackageType type, Double price) {
        this();
        this.name = name;
        this.type = type;
        this.price = price;
    }

    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PackageType getType() {
        return type;
    }

    public void setType(PackageType type) {
        this.type = type;
    }

    public Integer getDurationDays() {
        return durationDays;
    }

    public void setDurationDays(Integer durationDays) {
        this.durationDays = durationDays;
    }

    public Integer getDailyProteinG() {
        return dailyProteinG;
    }

    public void setDailyProteinG(Integer dailyProteinG) {
        this.dailyProteinG = dailyProteinG;
    }

    public Integer getDailyCarbsG() {
        return dailyCarbsG;
    }

    public void setDailyCarbsG(Integer dailyCarbsG) {
        this.dailyCarbsG = dailyCarbsG;
    }

    public Integer getMealsPerDay() {
        return mealsPerDay;
    }

    public void setMealsPerDay(Integer mealsPerDay) {
        this.mealsPerDay = mealsPerDay;
    }

    public Integer getMealsPerWeek() {
        return mealsPerWeek;
    }

    public void setMealsPerWeek(Integer mealsPerWeek) {
        this.mealsPerWeek = mealsPerWeek;
    }

    public Integer getFrequencyPerWeek() {
        return frequencyPerWeek;
    }

    public void setFrequencyPerWeek(Integer frequencyPerWeek) {
        this.frequencyPerWeek = frequencyPerWeek;
    }

    public String getProductionDays() {
        return productionDays;
    }

    public void setProductionDays(String productionDays) {
        this.productionDays = productionDays;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<Meal> getMeals() {
        return meals;
    }

    public void setMeals(List<Meal> meals) {
        this.meals = meals;
    }
}
