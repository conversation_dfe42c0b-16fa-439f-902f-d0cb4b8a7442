package com.prodiet.dto;

import java.time.LocalDate;
import java.util.List;

public class ProductionReportDto {

    private LocalDate reportDate;
    private int totalActiveSubscriptions;
    private int totalMealsToDeliver;
    private List<IngredientSummaryDto> ingredientSummary;
    private List<CustomerMealDto> customerBreakdown;

    // Constructors
    public ProductionReportDto() {}

    public ProductionReportDto(LocalDate reportDate) {
        this.reportDate = reportDate;
    }

    // Getters and Setters
    public LocalDate getReportDate() {
        return reportDate;
    }

    public void setReportDate(LocalDate reportDate) {
        this.reportDate = reportDate;
    }

    public int getTotalActiveSubscriptions() {
        return totalActiveSubscriptions;
    }

    public void setTotalActiveSubscriptions(int totalActiveSubscriptions) {
        this.totalActiveSubscriptions = totalActiveSubscriptions;
    }

    public int getTotalMealsToDeliver() {
        return totalMealsToDeliver;
    }

    public void setTotalMealsToDeliver(int totalMealsToDeliver) {
        this.totalMealsToDeliver = totalMealsToDeliver;
    }

    public List<IngredientSummaryDto> getIngredientSummary() {
        return ingredientSummary;
    }

    public void setIngredientSummary(List<IngredientSummaryDto> ingredientSummary) {
        this.ingredientSummary = ingredientSummary;
    }

    public List<CustomerMealDto> getCustomerBreakdown() {
        return customerBreakdown;
    }

    public void setCustomerBreakdown(List<CustomerMealDto> customerBreakdown) {
        this.customerBreakdown = customerBreakdown;
    }

    // Inner DTO classes
    public static class IngredientSummaryDto {
        private String ingredientName;
        private int totalWeightG;
        private String unit = "g";

        public IngredientSummaryDto() {}

        public IngredientSummaryDto(String ingredientName, int totalWeightG) {
            this.ingredientName = ingredientName;
            this.totalWeightG = totalWeightG;
        }

        // Getters and Setters
        public String getIngredientName() {
            return ingredientName;
        }

        public void setIngredientName(String ingredientName) {
            this.ingredientName = ingredientName;
        }

        public int getTotalWeightG() {
            return totalWeightG;
        }

        public void setTotalWeightG(int totalWeightG) {
            this.totalWeightG = totalWeightG;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }
    }

    public static class CustomerMealDto {
        private Long customerId;
        private String customerName;
        private String customerEmail;
        private int mealMultiplier;
        private String fridayPreference;
        private List<MealDetailDto> meals;

        public CustomerMealDto() {}

        public CustomerMealDto(Long customerId, String customerName, String customerEmail, 
                              int mealMultiplier, String fridayPreference) {
            this.customerId = customerId;
            this.customerName = customerName;
            this.customerEmail = customerEmail;
            this.mealMultiplier = mealMultiplier;
            this.fridayPreference = fridayPreference;
        }

        // Getters and Setters
        public Long getCustomerId() {
            return customerId;
        }

        public void setCustomerId(Long customerId) {
            this.customerId = customerId;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getCustomerEmail() {
            return customerEmail;
        }

        public void setCustomerEmail(String customerEmail) {
            this.customerEmail = customerEmail;
        }

        public int getMealMultiplier() {
            return mealMultiplier;
        }

        public void setMealMultiplier(int mealMultiplier) {
            this.mealMultiplier = mealMultiplier;
        }

        public String getFridayPreference() {
            return fridayPreference;
        }

        public void setFridayPreference(String fridayPreference) {
            this.fridayPreference = fridayPreference;
        }

        public List<MealDetailDto> getMeals() {
            return meals;
        }

        public void setMeals(List<MealDetailDto> meals) {
            this.meals = meals;
        }
    }

    public static class MealDetailDto {
        private Long mealId;
        private String mealName;
        private String mealType;
        private List<ComponentDetailDto> components;

        public MealDetailDto() {}

        public MealDetailDto(Long mealId, String mealName, String mealType) {
            this.mealId = mealId;
            this.mealName = mealName;
            this.mealType = mealType;
        }

        // Getters and Setters
        public Long getMealId() {
            return mealId;
        }

        public void setMealId(Long mealId) {
            this.mealId = mealId;
        }

        public String getMealName() {
            return mealName;
        }

        public void setMealName(String mealName) {
            this.mealName = mealName;
        }

        public String getMealType() {
            return mealType;
        }

        public void setMealType(String mealType) {
            this.mealType = mealType;
        }

        public List<ComponentDetailDto> getComponents() {
            return components;
        }

        public void setComponents(List<ComponentDetailDto> components) {
            this.components = components;
        }
    }

    public static class ComponentDetailDto {
        private String ingredientName;
        private int weightG;
        private int totalWeightG; // weightG * mealMultiplier
        private String preparationNotes;

        public ComponentDetailDto() {}

        public ComponentDetailDto(String ingredientName, int weightG, int totalWeightG, String preparationNotes) {
            this.ingredientName = ingredientName;
            this.weightG = weightG;
            this.totalWeightG = totalWeightG;
            this.preparationNotes = preparationNotes;
        }

        // Getters and Setters
        public String getIngredientName() {
            return ingredientName;
        }

        public void setIngredientName(String ingredientName) {
            this.ingredientName = ingredientName;
        }

        public int getWeightG() {
            return weightG;
        }

        public void setWeightG(int weightG) {
            this.weightG = weightG;
        }

        public int getTotalWeightG() {
            return totalWeightG;
        }

        public void setTotalWeightG(int totalWeightG) {
            this.totalWeightG = totalWeightG;
        }

        public String getPreparationNotes() {
            return preparationNotes;
        }

        public void setPreparationNotes(String preparationNotes) {
            this.preparationNotes = preparationNotes;
        }
    }
}
