Pro Diet: Backend Development Task List (Spring Boot)

This task list breaks down the backend development into actionable steps. It's designed to be followed sequentially to ensure a logical build process.
Phase 1: Project Foundation & Core Models

    [ ] 1.1: Initialize Spring Boot Project

        Create a new Spring Boot project with Maven/Gradle.

        Include necessary dependencies: Spring Web, Spring Data JPA, Spring Security, SQLite Driver, and SpringDoc (for Swagger).

    [ ] 1.2: Database & Swagger Configuration

        Configure application.properties to connect to the SQLite database.

        Configure Swagger/SpringDoc to generate API documentation automatically. Set up basic information like API title and version (/api/v1).

    [ ] 1.3: Create Core JPA Entities

        Create the following JPA entity classes based on the schema in the requirements:

            User (with roles: ADMIN, CUSTOMER)

            Package

            Meal

            MealComponent

            Subscription

            MealExclusion (many-to-many link table)

            DietaryPreference

            PausedDay

    [ ] 1.4: Implement Authentication & Authorization

        Set up Spring Security.

        Implement JWT (JSON Web Token) for authenticating API requests.

        Configure Role-Based Access Control (RBAC) to protect endpoints based on ADMIN and CUSTOMER roles.

Phase 2: Core Feature Implementation - CRUD Operations

    [ ] 2.1: Implement Package Management (Admin)

        Create PackageRepository, PackageService, and PackageController.

        Implement all CRUD endpoints (POST, GET, GET by ID, PUT, DELETE) for managing packages. Secure them for ADMIN access only.

    [ ] 2.2: Implement Customer Management (Admin)

        Create UserRepository, UserService, and UserController.

        Implement the POST /customers endpoint for an Admin to create a new CUSTOMER user. Ensure passwords are securely hashed.

    [ ] 2.3: Implement Subscription Creation (Admin)

        Create SubscriptionRepository, SubscriptionService, and SubscriptionController.

        Implement the POST /subscriptions endpoint for an Admin to assign a package to a customer.

        Logic: This service must calculate the initial endDate based on the package duration and handle the initial Friday extension logic (if the customer opts out of Friday meals).

Phase 3: Customer-Facing Features

    [ ] 3.1: Implement Preference Management (Customer)

        Implement the PUT /customers/{id}/preferences endpoint.

        This should allow a logged-in customer to update their own MealExclusions and DietaryPreferences.

    [ ] 3.2: Implement Subscription Retrieval (Customer)

        Implement the GET /customers/{id}/subscription endpoint for a customer to view their active subscription details.

        Implement the GET /subscriptions/{id}/paused-days endpoint.

Phase 4: Complex Business Logic Implementation

    [ ] 4.1: Implement Subscription Pause Logic (Customer)

        In the SubscriptionService, implement the logic for the POST /subscriptions/{id}/pause endpoint.

        Validation:

            Check if the user has exceeded the 7-day pause limit.

            Enforce the 48-hour advance notice rule.

            Enforce the 72-hour advance notice rule for Saturdays.

        Action: If valid, add the date to the PausedDays table and update the endDate of the Subscription.

    [ ] 4.2: Implement Friday Service Logic

        Ensure the logic to handle the customer's Friday preference (friday_preference in the Subscriptions table) is robust.

        This logic will be primarily used by the Production Reporting services in the next phase.

Phase 5: Production & Reporting

    [ ] 5.1: Create Production Calculation Service

        Create a ProductionService.

        This service will contain the core logic for the daily reports. It needs to query all subscriptions active for a given day, filter out paused ones, and account for the Friday meal doubling rule.

    [ ] 5.2: Implement Aggregated Production Report

        Implement the method in ProductionService that calculates the total weight of each ingredient for the day.

        Create the GET /production/today/aggregated endpoint in a new ProductionController that uses this service.

    [ ] 5.3: Implement Customer Breakdown Report

        Implement the method in ProductionService that generates a detailed meal-by-meal, component-by-component list for each active customer.

        Create the GET /production/today/customer-breakdown endpoint.

Phase 6: Finalization

    [ ] 6.1: Implement Unit & Integration Tests

        Write tests for critical service logic, especially for the subscription pause and production calculation services.

    [ ] 6.2: Review & Refine

        Review all code for adherence to SOLID and DRY principles.

        Verify all Swagger documentation is clear and complete.

        Test all endpoints manually for correctness.
