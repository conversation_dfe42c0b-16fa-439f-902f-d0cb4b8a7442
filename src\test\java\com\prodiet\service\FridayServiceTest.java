package com.prodiet.service;

import com.prodiet.entity.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class FridayServiceTest {

    @InjectMocks
    private FridayService fridayService;

    private User customer;
    private com.prodiet.entity.Package testPackage;
    private Subscription subscriptionReceiveOnThursday;
    private Subscription subscriptionSkipAndExtend;

    @BeforeEach
    void setUp() {
        customer = new User("John Doe", "<EMAIL>", "password", User.Role.CUSTOMER);
        customer.setId(1L);

        testPackage = new com.prodiet.entity.Package("Test Package", com.prodiet.entity.Package.PackageType.STANDARD,
                100.0);
        testPackage.setId(1L);
        testPackage.setDurationDays(30);
        testPackage.setMealsPerDay(3);

        // Subscription with RECEIVE_ON_THURSDAY preference
        subscriptionReceiveOnThursday = new Subscription(customer, testPackage,
                LocalDate.now().minusDays(5), LocalDate.now().plusDays(25));
        subscriptionReceiveOnThursday.setId(1L);
        subscriptionReceiveOnThursday.setStatus(Subscription.SubscriptionStatus.ACTIVE);
        subscriptionReceiveOnThursday.setFridayPreference(Subscription.FridayPreference.RECEIVE_ON_THURSDAY);
        subscriptionReceiveOnThursday.setPausedDays(new ArrayList<>());

        // Subscription with SKIP_AND_EXTEND preference
        subscriptionSkipAndExtend = new Subscription(customer, testPackage,
                LocalDate.now().minusDays(5), LocalDate.now().plusDays(25));
        subscriptionSkipAndExtend.setId(2L);
        subscriptionSkipAndExtend.setStatus(Subscription.SubscriptionStatus.ACTIVE);
        subscriptionSkipAndExtend.setFridayPreference(Subscription.FridayPreference.SKIP_AND_EXTEND);
        subscriptionSkipAndExtend.setPausedDays(new ArrayList<>());
    }

    @Test
    void testShouldReceiveMealsOnDate_Monday() {
        LocalDate monday = getNextDayOfWeek(DayOfWeek.MONDAY);

        assertTrue(fridayService.shouldReceiveMealsOnDate(subscriptionReceiveOnThursday, monday));
        assertTrue(fridayService.shouldReceiveMealsOnDate(subscriptionSkipAndExtend, monday));
    }

    @Test
    void testShouldReceiveMealsOnDate_Thursday_ReceiveOnThursday() {
        LocalDate thursday = getNextDayOfWeek(DayOfWeek.THURSDAY);

        assertTrue(fridayService.shouldReceiveMealsOnDate(subscriptionReceiveOnThursday, thursday));
    }

    @Test
    void testShouldReceiveMealsOnDate_Thursday_SkipAndExtend() {
        LocalDate thursday = getNextDayOfWeek(DayOfWeek.THURSDAY);

        assertTrue(fridayService.shouldReceiveMealsOnDate(subscriptionSkipAndExtend, thursday));
    }

    @Test
    void testShouldReceiveMealsOnDate_Friday() {
        LocalDate friday = getNextDayOfWeek(DayOfWeek.FRIDAY);

        // Both preferences should not receive meals on Friday
        assertFalse(fridayService.shouldReceiveMealsOnDate(subscriptionReceiveOnThursday, friday));
        assertFalse(fridayService.shouldReceiveMealsOnDate(subscriptionSkipAndExtend, friday));
    }

    @Test
    void testShouldReceiveFridayMealsOnThursday() {
        LocalDate thursday = getNextDayOfWeek(DayOfWeek.THURSDAY);
        LocalDate friday = getNextDayOfWeek(DayOfWeek.FRIDAY);

        assertTrue(fridayService.shouldReceiveFridayMealsOnThursday(subscriptionReceiveOnThursday, thursday));
        assertFalse(fridayService.shouldReceiveFridayMealsOnThursday(subscriptionSkipAndExtend, thursday));

        // Should return false for non-Thursday dates
        assertFalse(fridayService.shouldReceiveFridayMealsOnThursday(subscriptionReceiveOnThursday, friday));
    }

    @Test
    void testGetEffectiveMealCount_RegularDays() {
        LocalDate monday = getNextDayOfWeek(DayOfWeek.MONDAY);

        assertEquals(3, fridayService.getEffectiveMealCount(subscriptionReceiveOnThursday, monday));
        assertEquals(3, fridayService.getEffectiveMealCount(subscriptionSkipAndExtend, monday));
    }

    @Test
    void testGetEffectiveMealCount_Thursday() {
        LocalDate thursday = getNextDayOfWeek(DayOfWeek.THURSDAY);

        // RECEIVE_ON_THURSDAY should get double meals (Thursday + Friday)
        assertEquals(6, fridayService.getEffectiveMealCount(subscriptionReceiveOnThursday, thursday));

        // SKIP_AND_EXTEND should get normal meals
        assertEquals(3, fridayService.getEffectiveMealCount(subscriptionSkipAndExtend, thursday));
    }

    @Test
    void testGetEffectiveMealCount_Friday() {
        LocalDate friday = getNextDayOfWeek(DayOfWeek.FRIDAY);

        // Both should get 0 meals on Friday
        assertEquals(0, fridayService.getEffectiveMealCount(subscriptionReceiveOnThursday, friday));
        assertEquals(0, fridayService.getEffectiveMealCount(subscriptionSkipAndExtend, friday));
    }

    @Test
    void testGetMealMultiplier() {
        LocalDate monday = getNextDayOfWeek(DayOfWeek.MONDAY);
        LocalDate thursday = getNextDayOfWeek(DayOfWeek.THURSDAY);
        LocalDate friday = getNextDayOfWeek(DayOfWeek.FRIDAY);

        // Regular days
        assertEquals(1, fridayService.getMealMultiplier(subscriptionReceiveOnThursday, monday));
        assertEquals(1, fridayService.getMealMultiplier(subscriptionSkipAndExtend, monday));

        // Thursday
        assertEquals(2, fridayService.getMealMultiplier(subscriptionReceiveOnThursday, thursday));
        assertEquals(1, fridayService.getMealMultiplier(subscriptionSkipAndExtend, thursday));

        // Friday
        assertEquals(0, fridayService.getMealMultiplier(subscriptionReceiveOnThursday, friday));
        assertEquals(0, fridayService.getMealMultiplier(subscriptionSkipAndExtend, friday));
    }

    @Test
    void testFilterActiveSubscriptionsForDate() {
        List<Subscription> subscriptions = List.of(subscriptionReceiveOnThursday, subscriptionSkipAndExtend);

        LocalDate monday = getNextDayOfWeek(DayOfWeek.MONDAY);
        LocalDate friday = getNextDayOfWeek(DayOfWeek.FRIDAY);

        // Monday: both should be included
        List<Subscription> mondayResult = fridayService.filterActiveSubscriptionsForDate(subscriptions, monday);
        assertEquals(2, mondayResult.size());

        // Friday: none should be included
        List<Subscription> fridayResult = fridayService.filterActiveSubscriptionsForDate(subscriptions, friday);
        assertEquals(0, fridayResult.size());
    }

    @Test
    void testGetSubscriptionsReceivingFridayMealsOnThursday() {
        List<Subscription> subscriptions = List.of(subscriptionReceiveOnThursday, subscriptionSkipAndExtend);
        LocalDate thursday = getNextDayOfWeek(DayOfWeek.THURSDAY);

        List<Subscription> result = fridayService.getSubscriptionsReceivingFridayMealsOnThursday(subscriptions,
                thursday);

        assertEquals(1, result.size());
        assertEquals(subscriptionReceiveOnThursday.getId(), result.get(0).getId());
    }

    @Test
    void testGetSubscriptionsReceivingFridayMealsOnThursday_InvalidDay() {
        List<Subscription> subscriptions = List.of(subscriptionReceiveOnThursday, subscriptionSkipAndExtend);
        LocalDate monday = getNextDayOfWeek(DayOfWeek.MONDAY);

        assertThrows(IllegalArgumentException.class, () -> {
            fridayService.getSubscriptionsReceivingFridayMealsOnThursday(subscriptions, monday);
        });
    }

    @Test
    void testGetNextDeliveryDate() {
        LocalDate startDate = getNextDayOfWeek(DayOfWeek.MONDAY);

        // For RECEIVE_ON_THURSDAY, next delivery should be the same Monday
        LocalDate nextDelivery1 = fridayService.getNextDeliveryDate(subscriptionReceiveOnThursday, startDate);
        assertEquals(startDate, nextDelivery1);

        // For SKIP_AND_EXTEND, next delivery should be the same Monday
        LocalDate nextDelivery2 = fridayService.getNextDeliveryDate(subscriptionSkipAndExtend, startDate);
        assertEquals(startDate, nextDelivery2);
    }

    @Test
    void testGetNextDeliveryDate_FromFriday() {
        LocalDate friday = getNextDayOfWeek(DayOfWeek.FRIDAY);
        LocalDate nextMonday = friday.plusDays(3);

        // From Friday, next delivery should be Monday
        LocalDate nextDelivery1 = fridayService.getNextDeliveryDate(subscriptionReceiveOnThursday, friday);
        assertEquals(nextMonday, nextDelivery1);

        LocalDate nextDelivery2 = fridayService.getNextDeliveryDate(subscriptionSkipAndExtend, friday);
        assertEquals(nextMonday, nextDelivery2);
    }

    @Test
    void testInactiveSubscription() {
        subscriptionReceiveOnThursday.setStatus(Subscription.SubscriptionStatus.CANCELLED);
        LocalDate monday = getNextDayOfWeek(DayOfWeek.MONDAY);

        assertFalse(fridayService.shouldReceiveMealsOnDate(subscriptionReceiveOnThursday, monday));
        assertEquals(0, fridayService.getEffectiveMealCount(subscriptionReceiveOnThursday, monday));
        assertEquals(0, fridayService.getMealMultiplier(subscriptionReceiveOnThursday, monday));
    }

    @Test
    void testPausedDay() {
        LocalDate monday = getNextDayOfWeek(DayOfWeek.MONDAY);
        PausedDay pausedDay = new PausedDay(monday, subscriptionReceiveOnThursday);
        subscriptionReceiveOnThursday.getPausedDays().add(pausedDay);

        assertFalse(fridayService.shouldReceiveMealsOnDate(subscriptionReceiveOnThursday, monday));
        assertEquals(0, fridayService.getEffectiveMealCount(subscriptionReceiveOnThursday, monday));
        assertEquals(0, fridayService.getMealMultiplier(subscriptionReceiveOnThursday, monday));
    }

    private LocalDate getNextDayOfWeek(DayOfWeek dayOfWeek) {
        LocalDate today = LocalDate.now();
        int daysUntilTarget = dayOfWeek.getValue() - today.getDayOfWeek().getValue();
        if (daysUntilTarget <= 0) {
            daysUntilTarget += 7;
        }
        return today.plusDays(daysUntilTarget);
    }
}
