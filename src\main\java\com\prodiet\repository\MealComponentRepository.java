package com.prodiet.repository;

import com.prodiet.entity.MealComponent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MealComponentRepository extends JpaRepository<MealComponent, Long> {

    List<MealComponent> findByMealId(Long mealId);

    @Query("SELECT mc FROM MealComponent mc WHERE mc.meal.id IN :mealIds")
    List<MealComponent> findByMealIds(@Param("mealIds") List<Long> mealIds);

    @Query("SELECT mc FROM MealComponent mc WHERE mc.ingredientName = :ingredientName")
    List<MealComponent> findByIngredientName(@Param("ingredientName") String ingredientName);

    @Query("SELECT DISTINCT mc.ingredientName FROM MealComponent mc ORDER BY mc.ingredientName")
    List<String> findAllIngredientNames();

    @Query("SELECT mc FROM MealComponent mc JOIN mc.meal m WHERE m.isActive = true")
    List<MealComponent> findByActiveMeals();
}
