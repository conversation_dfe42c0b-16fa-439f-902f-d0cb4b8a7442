package com.prodiet.controller;

import com.prodiet.dto.PreferencesResponse;
import com.prodiet.dto.UpdatePreferencesRequest;
import com.prodiet.entity.User;
import com.prodiet.service.PreferenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/customers")
@Tag(name = "Customer Preferences", description = "Customer preference management APIs")
@SecurityRequirement(name = "Bearer Authentication")
public class CustomerPreferenceController {

    @Autowired
    private PreferenceService preferenceService;

    @GetMapping("/{id}/preferences")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('CUSTOMER') and #id == authentication.principal.id)")
    @Operation(summary = "Get customer preferences", 
               description = "Retrieve meal exclusions and dietary preferences for a customer")
    public ResponseEntity<PreferencesResponse> getCustomerPreferences(
            @Parameter(description = "Customer ID") @PathVariable Long id,
            Authentication authentication) {
        
        // Additional security check for customers
        if (authentication.getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals("ROLE_CUSTOMER"))) {
            User currentUser = (User) authentication.getPrincipal();
            if (!currentUser.getId().equals(id)) {
                return ResponseEntity.status(403).build();
            }
        }
        
        PreferencesResponse preferences = preferenceService.getCustomerPreferences(id);
        return ResponseEntity.ok(preferences);
    }

    @PutMapping("/{id}/preferences")
    @PreAuthorize("hasRole('ADMIN') or (hasRole('CUSTOMER') and #id == authentication.principal.id)")
    @Operation(summary = "Update customer preferences", 
               description = "Update meal exclusions and dietary preferences for a customer")
    public ResponseEntity<PreferencesResponse> updateCustomerPreferences(
            @Parameter(description = "Customer ID") @PathVariable Long id,
            @Valid @RequestBody UpdatePreferencesRequest request,
            Authentication authentication) {
        
        // Additional security check for customers
        if (authentication.getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals("ROLE_CUSTOMER"))) {
            User currentUser = (User) authentication.getPrincipal();
            if (!currentUser.getId().equals(id)) {
                return ResponseEntity.status(403).build();
            }
        }
        
        PreferencesResponse updatedPreferences = preferenceService.updateCustomerPreferences(id, request);
        return ResponseEntity.ok(updatedPreferences);
    }
}
