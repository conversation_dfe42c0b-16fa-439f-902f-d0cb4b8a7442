Pro Diet: Backend Requirements (Spring Boot)
1. Project Overview

This document specifies the backend requirements for the "Pro Diet" application. The backend will be a Spring Boot application responsible for all business logic, data processing, and API endpoints needed to power the admin dashboard and customer portal.
2. Technology Stack

    Framework: Spring Boot

    Database: SQLite

    API Documentation: Swagger

3. Core Logic & API Endpoints
3.1. API Design

    The API should be designed following RESTful principles.

    All endpoints must be documented using Swagger/OpenAPI.

    Implement versioning for the API (e.g., /api/v1/...).

3.2. Authentication & Authorization

    Implement role-based access control (RBAC).

        Admin: Full access to all endpoints.

        Customer: Access to their own data, preferences, and subscription management.

    The admin will create customer accounts; the system should handle credential management securely.

3.3. Package Management Endpoints

    POST /packages - Create a new standard or add-on package.

    GET /packages - Retrieve a list of all available packages.

    GET /packages/{id} - Retrieve details of a specific package.

    PUT /packages/{id} - Update a package's details.

    DELETE /packages/{id} - Delete a package.

3.4. Customer & Subscription Endpoints

    POST /customers - Create a new customer account (Admin only).

    POST /subscriptions - Assign a package to a customer, creating a new subscription.

    GET /customers/{id}/subscription - Retrieve a customer's current subscription details.

    PUT /customers/{id}/preferences - Allow a customer to update meal exclusions and dietary preferences.

    POST /subscriptions/{id}/pause - Endpoint for a customer to request a subscription pause.

    GET /subscriptions/{id}/paused-days - Retrieve the days a subscription has been paused.

3.5. Production & Kitchen Management Logic

This is the most critical backend feature and requires robust service-layer logic.

    Daily Production Calculation Service:

        A scheduled or on-demand service that calculates the total quantity of each food item needed for the current day.

        This service must:

            Iterate through all active subscriptions for the day.

            Exclude customers who have paused their subscription for the day.

            Factor in the Friday meal logic (doubling Thursday's production for relevant customers).

            Aggregate the component weights for all meals to be produced.

    API Endpoints for Reports:

        GET /production/today/aggregated - Returns the total weight of every food item required for the day.

        GET /production/today/customer-breakdown - Returns a detailed list of meals and component weights for every customer receiving meals today.

4. Business Rule Implementation
4.1. Subscription Pause Logic

    The /subscriptions/{id}/pause endpoint must contain validation logic to enforce:

        A maximum of 7 paused days per subscription term.

        The 48-hour advance notice rule.

        The 72-hour advance notice rule for pausing a Saturday.

    Upon successful validation, the backend must:

        Record the paused date(s) in the database.

        Recalculate and update the subscription's endDate by adding the number of paused days.

4.2. Friday Service Logic

    The backend must store a customer's choice regarding Friday meals.

    When calculating production and subscription dates, the system must check this preference:

        If Opt-In: The production service must double the meal components for that customer on the preceding Thursday.

        If Opt-Out: The subscription's endDate must be automatically extended by one day for every Friday within the active subscription period. This calculation should likely happen upon subscription creation and be adjusted if the subscription changes.

5. Database Schema (High-Level)

The database should include, but is not limited to, the following tables:

    Users (id, name, role, credentials)

    Packages (id, name, type, duration, protein_g, carbs_g, meals_per_day, etc.)

    Meals (id, name, description)

    MealComponents (meal_id, ingredient_name, weight_g)

    Subscriptions (id, user_id, package_id, startDate, endDate, friday_preference)

    MealExclusions (subscription_id, meal_id)

    DietaryPreferences (subscription_id, preference_text)

    PausedDays (subscription_id, date)

6. Code Quality

    The application must be built following industry best practices for Spring Boot.

    Adhere strictly to SOLID and DRY principles to ensure a maintainable and scalable codebase.
